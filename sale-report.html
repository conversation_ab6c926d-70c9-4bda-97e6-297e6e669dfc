<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Sale Report - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      overflow-x: hidden;
      background-color: #1c1c1c;
      color: #f8f9fa;
    }

    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }

    .sidebar a {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
    }

    .sidebar a:hover {
      background-color: #444;
      color: #ff9900;
      padding-left: 25px;
    }

    .content {
      margin-left: 250px;
      padding: 20px;
      transition: all 0.3s;
    }

    .sidebar-collapsed {
      width: 0;
      overflow: hidden;
    }

    .content-expanded {
      margin-left: 0;
    }

    .toggle-btn {
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
    }

    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }

    .glass-card {
      background: rgba(34, 34, 34, 0.7);
      border-radius: 22px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.22), 0 0 0 4px rgba(255, 204, 102, 0.10);
      border: 1.5px solid rgba(255, 204, 102, 0.18);
      backdrop-filter: blur(14px) brightness(1.08);
      padding: 32px 28px 28px 28px;
      animation: fadeIn 0.7s;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .stats-card {
      background: linear-gradient(135deg, #ffcc66 0%, #ff9900 100%);
      color: #000;
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 4px 15px rgba(255, 204, 102, 0.3);
    }

    .table-dark {
      background-color: rgba(34, 34, 34, 0.9);
      border-radius: 10px;
    }

    .table-dark th {
      background-color: #ffcc66;
      color: #000;
      border: none;
    }

    .table-dark td {
      border-color: #444;
      color: #f8f9fa;
    }

    .btn-warning {
      background-color: #ffcc66;
      border-color: #ffcc66;
      color: #000;
    }

    .btn-warning:hover {
      background-color: #ff9900;
      border-color: #ff9900;
      color: white;
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
      }
      .sidebar.sidebar-collapsed {
        width: 0 !important;
      }
      .content {
        margin-left: 0 !important;
      }
    }

    /* Print Styles for Sale Report */
    @media print {
      body {
        background-color: white !important;
        color: black !important;
        font-family: Arial, sans-serif;
      }

      .sidebar, .toggle-btn {
        display: none !important;
      }

      .content {
        margin-left: 0 !important;
        padding: 0 !important;
      }

      .glass-card {
        background: white !important;
        border: none !important;
        box-shadow: none !important;
        padding: 20px !important;
      }

      .print-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 3px solid #28a745;
        padding-bottom: 15px;
      }

      .print-header h1 {
        color: #28a745 !important;
        font-size: 28px;
        margin: 0;
        font-weight: bold;
      }

      .print-header .company-info {
        color: #666 !important;
        font-size: 14px;
        margin-top: 5px;
      }

      .stats-row {
        display: flex;
        justify-content: space-between;
        margin: 20px 0;
        border: 2px solid #28a745;
        padding: 15px;
        background-color: #f8f9fa !important;
      }

      .stat-item {
        text-align: center;
        flex: 1;
      }

      .stat-item h4 {
        color: #28a745 !important;
        font-size: 18px;
        margin: 0;
      }

      .stat-item p {
        color: #666 !important;
        font-size: 12px;
        margin: 5px 0 0 0;
      }

      .table {
        border-collapse: collapse;
        width: 100%;
        margin-top: 20px;
      }

      .table th {
        background-color: #28a745 !important;
        color: white !important;
        padding: 12px 8px;
        text-align: left;
        font-weight: bold;
        border: 1px solid #28a745;
      }

      .table td {
        padding: 10px 8px;
        border: 1px solid #ddd;
        color: black !important;
      }

      .table tbody tr:nth-child(even) {
        background-color: #f8f9fa !important;
      }

      .print-footer {
        margin-top: 30px;
        text-align: center;
        font-size: 12px;
        color: #666 !important;
        border-top: 1px solid #ddd;
        padding-top: 15px;
      }

      .btn, .form-control, .row.mb-4 {
        display: none !important;
      }
    }
  </style>
</head>

<body>

  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="./search.html">Search Customer</a>
    <a href="#" id="reportsMenu" onclick="toggleReportsMenu()">Reports ▼</a>
    <div id="reportsSubMenu" style="display:none; margin-left:20px;">
      <a href="./sale-report.html">Sale Report</a>
      <a href="./return-report.html">Return Report</a>
      <a href="./individual-report.html">Individual Report</a>
    </div>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./add-ride.html">Add Ride</a>
    <a href="./setting.html">Settings</a>
  </div>

  <div class="content" id="content">
    <div class="container-fluid">
      
      <!-- Sale Report Section -->
      <div id="saleReportSection">
        <div class="glass-card">
          <!-- Print Header (only visible when printing) -->
          <div class="print-header" style="display: none;">
            <h1>FUN LAND - SALES REPORT</h1>
            <div class="company-info">
              Generated on: <span id="printDate"></span> | Fun Land Amusement Park
            </div>
          </div>

          <h2 class="text-warning mb-4">
            <i class="bi bi-graph-up"></i> Sale Report
          </h2>
          
          <!-- Print Stats (only visible when printing) -->
          <div class="stats-row" style="display: none;">
            <div class="stat-item">
              <h4 id="printTotalSales">PKR 0</h4>
              <p>Total Sales</p>
            </div>
            <div class="stat-item">
              <h4 id="printTodaySales">PKR 0</h4>
              <p>Today's Sales</p>
            </div>
            <div class="stat-item">
              <h4 id="printTotalTransactions">0</h4>
              <p>Total Transactions</p>
            </div>
            <div class="stat-item">
              <h4 id="printAvgSale">PKR 0</h4>
              <p>Average Sale</p>
            </div>
          </div>

          <!-- Stats Cards -->
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="totalSales">PKR 0</h4>
                <p class="mb-0">Total Sales</p>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="todaySales">PKR 0</h4>
                <p class="mb-0">Today's Sales</p>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="totalTransactions">0</h4>
                <p class="mb-0">Total Transactions</p>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="avgSale">PKR 0</h4>
                <p class="mb-0">Average Sale</p>
              </div>
            </div>
          </div>

          <!-- Filter Section -->
          <div class="row mb-4">
            <div class="col-md-3">
              <label class="form-label text-warning">From Date</label>
              <input type="date" id="fromDate" class="form-control bg-dark text-light border-warning">
            </div>
            <div class="col-md-3">
              <label class="form-label text-warning">To Date</label>
              <input type="date" id="toDate" class="form-control bg-dark text-light border-warning">
            </div>
            <div class="col-md-3">
              <label class="form-label text-warning">Customer ID</label>
              <input type="text" id="customerFilter" class="form-control bg-dark text-light border-warning" placeholder="Enter Customer ID">
            </div>
            <div class="col-md-3 d-flex align-items-end">
              <button class="btn btn-warning w-100" onclick="filterSales()">
                <i class="bi bi-funnel"></i> Filter
              </button>
            </div>
          </div>

          <!-- Sales Table -->
          <div class="table-responsive">
            <table class="table table-dark table-striped">
              <thead>
                <tr>
                  <th>Transaction ID</th>
                  <th>Date & Time</th>
                  <th>Customer ID</th>
                  <th>Customer Name</th>
                  <th>Amount</th>
                </tr>
              </thead>
              <tbody id="salesTableBody">
                <!-- Sales data will be populated here -->
              </tbody>
            </table>
          </div>

          <!-- Print Footer (only visible when printing) -->
          <div class="print-footer" style="display: none;">
            <p>This report was generated by Fun Land Management System</p>
            <p>For any queries, contact: <EMAIL> | Phone: +92-XXX-XXXXXXX</p>
          </div>

          <!-- Export Buttons -->
          <div class="row mt-4">
            <div class="col-md-6">
              <button class="btn btn-success w-100" onclick="exportToExcel()">
                <i class="bi bi-file-earmark-excel"></i> Export to Excel
              </button>
            </div>
            <div class="col-md-6">
              <button class="btn btn-info w-100" onclick="printReport()">
                <i class="bi bi-printer"></i> Print Report
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Sale Section -->
      <div id="saleSection" style="display:none;">
        <div class="glass-card mx-auto" style="max-width:500px;">
          <h2 class="text-success mb-4 text-center">
            <i class="bi bi-cart-plus"></i> Make Sale
          </h2>
          
          <div class="mb-3" id="saleScanSection">
            <button class="btn btn-primary w-100" onclick="scanSaleCard()">
              <i class="bi bi-upc-scan"></i> Scan Customer Card
            </button>
          </div>

          <div id="saleForm" style="display:none;">
            <div class="row mb-3">
              <div class="col">
                <label class="form-label text-warning">Customer ID</label>
                <div class="fw-bold text-info" id="saleCustomerId"></div>
              </div>
              <div class="col">
                <label class="form-label text-warning">Customer Name</label>
                <div class="fw-bold text-info" id="saleCustomerName"></div>
              </div>
            </div>
            
            <div class="mb-3">
              <label class="form-label text-warning">Current Balance</label>
              <div class="fw-bold fs-4 text-success" id="saleCurrentBalance"></div>
            </div>

            <div class="mb-3">
              <label for="saleAmount" class="form-label text-warning">Sale Amount</label>
              <input type="number" id="saleAmount" class="form-control bg-dark text-light border-warning" min="1" placeholder="Enter sale amount">
            </div>



            <button class="btn btn-success w-100 mt-3" onclick="processSale()">
              <i class="bi bi-check-circle"></i> Process Sale
            </button>
          </div>
        </div>
      </div>

    </div>
  </div>

  <script>
    // Sample sales data
    let salesData = [
      {
        id: 'TXN001',
        date: '2024-01-15 14:30:00',
        customerId: 'CUST001',
        customerName: 'Ali Khan',
        amount: 250,
        type: 'Game',
        status: 'Completed'
      },
      {
        id: 'TXN002', 
        date: '2024-01-15 15:45:00',
        customerId: 'CUST002',
        customerName: 'Sara Ahmed',
        amount: 180,
        type: 'Food',
        status: 'Completed'
      },
      {
        id: 'TXN003',
        date: '2024-01-15 16:20:00', 
        customerId: 'CUST003',
        customerName: 'Hassan Ali',
        amount: 320,
        type: 'Game',
        status: 'Completed'
      }
    ];

    let saleCustomer = null;

    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');

      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      toggleBtn.classList.toggle('active');

      toggleBtn.innerText = toggleBtn.innerText === '☰' ? '✖' : '☰';
    }

    function toggleReportsMenu() {
      const subMenu = document.getElementById('reportsSubMenu');
      const reportsMenu = document.getElementById('reportsMenu');

      if (subMenu.style.display === 'none' || subMenu.style.display === '') {
        subMenu.style.display = 'block';
        reportsMenu.innerHTML = 'Reports ▲';
      } else {
        subMenu.style.display = 'none';
        reportsMenu.innerHTML = 'Reports ▼';
      }
    }

    function showSale() {
      document.getElementById('saleReportSection').style.display = 'none';
      document.getElementById('saleSection').style.display = 'block';
    }

    function showReports() {
      document.getElementById('saleSection').style.display = 'none';
      document.getElementById('saleReportSection').style.display = 'block';
      loadSalesReport();
    }

    function loadSalesReport() {
      updateStats();
      populateSalesTable(salesData);
    }

    function updateStats() {
      const totalSales = salesData.reduce((sum, sale) => sum + sale.amount, 0);
      const todaysSales = salesData.filter(sale => 
        new Date(sale.date).toDateString() === new Date().toDateString()
      ).reduce((sum, sale) => sum + sale.amount, 0);
      
      document.getElementById('totalSales').textContent = `PKR ${totalSales}`;
      document.getElementById('todaySales').textContent = `PKR ${todaysSales}`;
      document.getElementById('totalTransactions').textContent = salesData.length;
      document.getElementById('avgSale').textContent = `PKR ${Math.round(totalSales / salesData.length) || 0}`;
    }

    function populateSalesTable(data) {
      const tbody = document.getElementById('salesTableBody');
      tbody.innerHTML = '';
      
      data.forEach(sale => {
        const row = `
          <tr>
            <td>${sale.id}</td>
            <td>${new Date(sale.date).toLocaleString()}</td>
            <td>${sale.customerId}</td>
            <td>${sale.customerName}</td>
            <td>PKR ${sale.amount}</td>
          </tr>
        `;
        tbody.innerHTML += row;
      });
    }

    function scanSaleCard() {
      saleCustomer = {
        id: 'CUST' + Math.floor(Math.random() * 1000),
        name: 'Customer ' + Math.floor(Math.random() * 100),
        balance: Math.floor(Math.random() * 2000) + 500
      };

      document.getElementById('saleScanSection').style.display = 'none';
      document.getElementById('saleForm').style.display = 'block';
      document.getElementById('saleCustomerId').textContent = saleCustomer.id;
      document.getElementById('saleCustomerName').textContent = saleCustomer.name;
      document.getElementById('saleCurrentBalance').textContent = `PKR ${saleCustomer.balance}`;
    }

    function processSale() {
      const amount = parseInt(document.getElementById('saleAmount').value);

      if (!amount || amount < 1) {
        alert('Please enter a valid sale amount!');
        return;
      }

      if (amount > saleCustomer.balance) {
        alert('Insufficient balance!');
        return;
      }

      // Add new sale to data
      const newSale = {
        id: 'TXN' + String(salesData.length + 1).padStart(3, '0'),
        date: new Date().toISOString(),
        customerId: saleCustomer.id,
        customerName: saleCustomer.name,
        amount: amount,
        type: 'Sale',
        status: 'Completed'
      };

      salesData.push(newSale);

      // Update customer balance
      saleCustomer.balance -= amount;
      document.getElementById('saleCurrentBalance').textContent = `PKR ${saleCustomer.balance}`;

      // Clear form
      document.getElementById('saleAmount').value = '';

      alert(`Sale processed successfully!\nTransaction ID: ${newSale.id}\nAmount: PKR ${amount}`);
    }

    function filterSales() {
      // Filter functionality can be implemented here
      alert('Filter functionality will be implemented based on requirements');
    }

    function exportToExcel() {
      alert('Excel export functionality will be implemented');
    }

    function printReport() {
      // Set print date
      document.getElementById('printDate').textContent = new Date().toLocaleDateString();

      // Copy stats to print version
      document.getElementById('printTotalSales').textContent = document.getElementById('totalSales').textContent;
      document.getElementById('printTodaySales').textContent = document.getElementById('todaySales').textContent;
      document.getElementById('printTotalTransactions').textContent = document.getElementById('totalTransactions').textContent;
      document.getElementById('printAvgSale').textContent = document.getElementById('avgSale').textContent;

      // Show print elements
      document.querySelector('.print-header').style.display = 'block';
      document.querySelector('.stats-row').style.display = 'flex';
      document.querySelector('.print-footer').style.display = 'block';

      // Print
      window.print();

      // Hide print elements after printing
      setTimeout(() => {
        document.querySelector('.print-header').style.display = 'none';
        document.querySelector('.stats-row').style.display = 'none';
        document.querySelector('.print-footer').style.display = 'none';
      }, 1000);
    }

    // Load sales report by default
    window.onload = function() {
      loadSalesReport();
    };
  </script>

</body>
</html>
