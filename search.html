<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Serach - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      background-color: #1c1c1c;
      color: #f8f9fa;
      min-height: 100vh;
      overflow-x: hidden;
    }
    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }
    .sidebar.sidebar-collapsed {
      width: 0 !important;
      padding: 0 !important;
      overflow: hidden;
    }
    .sidebar a, .sidebar #reportsMenu {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
      transition: background 0.3s, color 0.3s, padding-left 0.3s;
    }
    .sidebar a:hover, .sidebar #reportsMenu:hover {
      background-color: #444;
      color: #ff9900;
      padding-left: 25px;
    }
    #reportsSubMenu {
      transition: max-height 0.4s, opacity 0.4s;
      overflow: hidden;
    }
    #reportsSubMenu[style*="display: block"] {
      opacity: 1;
      max-height: 200px;
    }
    #reportsSubMenu[style*="display: none"] {
      opacity: 0;
      max-height: 0;
    }
    #reportsSubMenu a {
      color: #ffcc66;
      padding: 10px 15px;
      display: block;
      text-decoration: none;
      font-size: 15px;
      transition: background 0.3s, color 0.3s;
    }
    #reportsSubMenu a:hover {
      background-color: #444;
      color: #ff9900;
    }
    .toggle-btn {
      position: absolute;
      top: 18px;
      left: 18px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
      font-size: 1.25rem; /* Chhota font size */
      padding: 4px 12px;
      border-radius: 8px;
      transition: background 0.3s;
    }
    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }
    .sidebar.sidebar-collapsed .toggle-btn {
      left: 18px; /* Sidebar collapse hone par bhi button sidebar ke andar rahe */
      position: absolute;
      top: 18px;
      display: block;
    }
    .content {
      margin-left: 250px;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: margin-left 0.3s;
    }
    .content.content-expanded {
      margin-left: 0 !important;
    }
    .glass-card {
      background: rgba(34, 34, 34, 0.7);
      border-radius: 22px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.22), 0 0 0 4px rgba(255,204,102,0.10);
      border: 1.5px solid rgba(255,204,102,0.18);
      backdrop-filter: blur(14px) brightness(1.08);
      padding: 32px 28px 28px 28px;
      animation: fadeIn 0.7s;
      position: relative;
      max-width: 900px;
      width: 100%;
      margin: 40px auto;
    }
    .card-icon-circle {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: linear-gradient(135deg, #ffcc66 60%, #fffbe6 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 18px;
      box-shadow: 0 2px 12px rgba(255,204,102,0.15);
    }
   
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px);}
      to { opacity: 1; transform: translateY(0);}
    }
    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
        padding-top: 60px;
      }
      .toggle-btn {
        left: 200px;
      }
      .sidebar.sidebar-collapsed ~ .toggle-btn {
        left: 10px;
      }
      .content {
        margin-left: 0 !important;
      }
      .glass-card {
        padding: 18px 8px 18px 8px;
      }
    }
  </style>
</head>
<body>
  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="./search.html">Search Customer</a>
    <a href="#" id="reportsMenu" onclick="toggleReportsMenu()">Reports ▼</a>
    <div id="reportsSubMenu" style="display:none; margin-left:20px;">
      <a href="./sale-report.html">Sale Report</a>
      <a href="./return-report.html">Return Report</a>
      <a href="./individual-report.html">Individual Report</a>
    </div>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./add-ride.html">Add Ride</a>
    <a href="./setting.html">Settings</a>
  </div>
  <div class="content" id="content">
    <div class="container mt-5">
    <div class="glass-card mb-4">
      <h2 class="mb-3">Search Customer</h2>
      <input type="text" class="form-control" placeholder="Enter customer name or ID...">
    </div>

    <div class="glass-card">
      <h4 class="mb-3">Customer Results</h4>
      <div class="table-responsive">
        <table class="table table-dark table-hover">
          <thead>
            <tr>
              <th scope="col">User ID</th>
              <th scope="col">User Name</th>
              <th scope="col">Contact Number</th>
              <th scope="col">Total Balance</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>001</td>
              <td>Sameer Ali</td>
              <td>0300-1234567</td>
              <td>PKR 25,000</td>
            </tr>
            <tr>
              <td>002</td>
              <td>Hunaiza Marhaba</td>
              <td>0311-7654321</td>
              <td>PKR 12,300</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
     
  </div>
  <script>
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');
      const openBtn = document.getElementById('openBtn');
      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      if (sidebar.classList.contains('sidebar-collapsed')) {
        sidebar.style.width = '0';
        toggleBtn.style.display = 'none';
        openBtn.style.display = 'block';
      } else {
        sidebar.style.width = '250px';
        toggleBtn.style.display = 'block';
        openBtn.style.display = 'none';
      }
    }
    function toggleReportsMenu() {
      const subMenu = document.getElementById('reportsSubMenu');
      subMenu.style.display = subMenu.style.display === 'none' ? 'block' : 'none';
    }
   
  </script>
</body>
</html>