<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Serach - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      background-color: #1c1c1c;
      color: #f8f9fa;
      min-height: 100vh;
      overflow-x: hidden;
    }
    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }
    .sidebar.sidebar-collapsed {
      width: 0 !important;
      padding: 0 !important;
      overflow: hidden;
    }
    .sidebar a, .sidebar #reportsMenu {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
      transition: background 0.3s, color 0.3s, padding-left 0.3s;
    }
    .sidebar a:hover, .sidebar #reportsMenu:hover {
      background-color: #444;
      color: #ff9900;
      padding-left: 25px;
    }
    #reportsSubMenu {
      transition: max-height 0.4s, opacity 0.4s;
      overflow: hidden;
    }
    #reportsSubMenu[style*="display: block"] {
      opacity: 1;
      max-height: 200px;
    }
    #reportsSubMenu[style*="display: none"] {
      opacity: 0;
      max-height: 0;
    }
    #reportsSubMenu a {
      color: #ffcc66;
      padding: 10px 15px;
      display: block;
      text-decoration: none;
      font-size: 15px;
      transition: background 0.3s, color 0.3s;
    }
    #reportsSubMenu a:hover {
      background-color: #444;
      color: #ff9900;
    }
    .toggle-btn {
      position: absolute;
      top: 18px;
      left: 18px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
      font-size: 1.25rem; /* Chhota font size */
      padding: 4px 12px;
      border-radius: 8px;
      transition: background 0.3s;
    }
    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }
    .sidebar.sidebar-collapsed .toggle-btn {
      left: 18px; /* Sidebar collapse hone par bhi button sidebar ke andar rahe */
      position: absolute;
      top: 18px;
      display: block;
    }
    .content {
      margin-left: 250px;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: margin-left 0.3s;
    }
    .content.content-expanded {
      margin-left: 0 !important;
    }
    .glass-card {
      background: rgba(34, 34, 34, 0.7);
      border-radius: 22px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.22), 0 0 0 4px rgba(255,204,102,0.10);
      border: 1.5px solid rgba(255,204,102,0.18);
      
      padding: 32px 28px 28px 28px;
      animation: fadeIn 0.7s;
      position: relative;
      max-width: 900px;
      width: 100%;
      margin: 40px auto;
    }
    .card-icon-circle {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: linear-gradient(135deg, #ffcc66 60%, #fffbe6 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 18px;
      box-shadow: 0 2px 12px rgba(255,204,102,0.15);
    }
     .form-control, .form-select, .form-check-input {
      background-color: #2e2e2e;
      color: #ffffff;
      border: 1px solid #444;
    }
    .form-control:focus, .form-select:focus, .form-check-input:focus {
      background-color: #2e2e2e;
      color: #ffffff;
      border-color: #ffcc00;
    }
    th {
      color: #ffcc00;
    }
    .dropdown-checklist {
      position: relative;
    }
    .dropdown-checklist .dropdown-menu {
  display: none;
  position: absolute;
  background-color: #2e2e2e;
  border: 1px solid #444;
  width: 100%;
  max-height: 200px;
  color: white;
  overflow-y: auto;
  z-index: 1050 !important; /* Updated z-index */
}
    .dropdown-checklist.show .dropdown-menu {
      display: block;
    }
    .dropdown-checklist .dropdown-toggle {
      width: 100%;
    }
   
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px);}
      to { opacity: 1; transform: translateY(0);}
    }
    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
        padding-top: 60px;
      }
      .toggle-btn {
        left: 200px;
      }
      .sidebar.sidebar-collapsed ~ .toggle-btn {
        left: 10px;
      }
      .content {
        margin-left: 0 !important;
      }
      .glass-card {
        padding: 18px 8px 18px 8px;
      }
    }
  </style>
</head>
<body>
  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="./search.html">Search Customer</a>
    <a href="#" id="reportsMenu" onclick="toggleReportsMenu()">Reports ▼</a>
    <div id="reportsSubMenu" style="display:none; margin-left:20px;">
      <a href="./sale-report.html">Sale Report</a>
      <a href="./return-report.html">Return Report</a>
      <a href="./individual-report.html">Individual Report</a>
    </div>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./add-ride.html">Add Ride</a>
    <a href="./setting.html">Settings</a>
  </div>
  <div class="content" id="content">
    <div class="container mt-5">
   <div class="glass-card mb-4">
    <h2 class="mb-3">Add Package</h2>
    <div class="row g-3">
      <div class="col-md-12">
        <label class="form-label">Package Name</label>
        <input type="text" id="dealName" class="form-control" placeholder="Enter deal name">
      </div>
      <div class="col-md-6">
        <label class="form-label">Select Rides</label>
        <div class="dropdown-checklist" id="rideDropdown">
          <button class="btn btn-dark dropdown-toggle" onclick="toggleDropdown()">
            <span id="selectedRidesDisplay">Select Rides</span>
          </button>
          <div class="dropdown-menu p-2 shadow position-absolute">
            <input type="text" class="form-control search-box" id="rideSearch" placeholder="Search rides..." onkeyup="filterRides()">
            <div id="rideList">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" value="Roller Coaster" onchange="updateSelected()">
                <label class="form-check-label">Roller Coaster</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" value="Ferris Wheel" onchange="updateSelected()">
                <label class="form-check-label">Ferris Wheel</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" value="Water Slide" onchange="updateSelected()">
                <label class="form-check-label">Water Slide</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" value="Haunted House" onchange="updateSelected()">
                <label class="form-check-label">Haunted House</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" value="Bumper Cars" onchange="updateSelected()">
                <label class="form-check-label">Bumper Cars</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" value="Drop Tower" onchange="updateSelected()">
                <label class="form-check-label">Drop Tower</label>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <label class="form-label">Enter Total Price</label>
        <input type="number" id="ridePrice" class="form-control" placeholder="Enter total price">
      </div>
      <div class="col-12">
        <button onclick="addPackage()" class="btn btn-warning text-dark w-100">Add Package Deal</button>
      </div>
    </div>
  </div>

  <div class="glass-card">
    <h4 class="mb-3">Package Deal List</h4>
    <div class="table-responsive">
      <table class="table table-dark table-hover">
        <thead>
          <tr>
            <th scope="col">Deal Name</th>
            <th scope="col">Rides</th>
            <th scope="col">Total Price</th>
          </tr>
        </thead>
        <tbody id="packageTable"></tbody>
      </table>
    </div>
  </div>
  </div>
     
  </div>
  <script>
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');
      const openBtn = document.getElementById('openBtn');
      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      if (sidebar.classList.contains('sidebar-collapsed')) {
        sidebar.style.width = '0';
        toggleBtn.style.display = 'none';
        openBtn.style.display = 'block';
      } else {
        sidebar.style.width = '250px';
        toggleBtn.style.display = 'block';
        openBtn.style.display = 'none';
      }
    }
    function toggleReportsMenu() {
      const subMenu = document.getElementById('reportsSubMenu');
      subMenu.style.display = subMenu.style.display === 'none' ? 'block' : 'none';
    }
   
  </script>

 <script>
  function toggleDropdown() {
    document.getElementById("rideDropdown").classList.toggle("show");
  }

  function updateSelected() {
    const checkboxes = document.querySelectorAll("#rideDropdown input[type='checkbox']");
    const selectedDisplay = document.getElementById("selectedRidesDisplay");
    const selected = Array.from(checkboxes)
      .filter(cb => cb.checked)
      .map(cb => `<span class='selected-ride'>${cb.value}</span>`);
    selectedDisplay.innerHTML = selected.length > 0 ? selected.join(' ') : "Select Rides";
  }

  function filterRides() {
    const input = document.getElementById("rideSearch");
    const filter = input.value.toLowerCase();
    const rideList = document.getElementById("rideList");
    const items = rideList.getElementsByClassName("form-check");

    for (let i = 0; i < items.length; i++) {
      const label = items[i].getElementsByTagName("label")[0];
      const text = label.textContent || label.innerText;
      items[i].style.display = text.toLowerCase().includes(filter) ? "" : "none";
    }
  }

  function addPackage() {
    const dealName = document.getElementById("dealName").value.trim();
    const checkboxes = document.querySelectorAll("#rideDropdown input[type='checkbox']");
    const price = document.getElementById("ridePrice").value;

    const selectedRides = Array.from(checkboxes)
      .filter(cb => cb.checked)
      .map(cb => cb.value);

    if (!dealName || selectedRides.length === 0 || !price) {
      alert("Please enter deal name, select at least one ride, and enter the price.");
      return;
    }

    const table = document.getElementById("packageTable");
    const row = document.createElement("tr");
    row.innerHTML = `
      <td>${dealName}</td>
      <td>${selectedRides.join(', ')}</td>
      <td>PKR ${parseInt(price).toLocaleString()}</td>
    `;
    table.appendChild(row);

    // Reset
    document.getElementById("dealName").value = "";
    checkboxes.forEach(cb => cb.checked = false);
    document.getElementById("ridePrice").value = "";
    document.getElementById("rideDropdown").classList.remove("show");
    updateSelected();
  }

  document.addEventListener("click", function(event) {
    const dropdown = document.getElementById("rideDropdown");
    if (!dropdown.contains(event.target)) {
      dropdown.classList.remove("show");
    }
  });
</script>
</body>
</html>