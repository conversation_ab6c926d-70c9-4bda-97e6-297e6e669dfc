<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Serach - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      background-color: #1c1c1c;
      color: #f8f9fa;
      min-height: 100vh;
      overflow-x: hidden;
    }
    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }
    .sidebar.sidebar-collapsed {
      width: 0 !important;
      padding: 0 !important;
      overflow: hidden;
    }
    .sidebar a, .sidebar #reportsMenu {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
      transition: background 0.3s, color 0.3s, padding-left 0.3s;
    }
    .sidebar a:hover, .sidebar #reportsMenu:hover {
      background-color: #444;
      color: #ff9900;
      padding-left: 25px;
    }
    #reportsSubMenu {
      transition: max-height 0.4s, opacity 0.4s;
      overflow: hidden;
    }
    #reportsSubMenu[style*="display: block"] {
      opacity: 1;
      max-height: 200px;
    }
    #reportsSubMenu[style*="display: none"] {
      opacity: 0;
      max-height: 0;
    }
    #reportsSubMenu a {
      color: #ffcc66;
      padding: 10px 15px;
      display: block;
      text-decoration: none;
      font-size: 15px;
      transition: background 0.3s, color 0.3s;
    }
    #reportsSubMenu a:hover {
      background-color: #444;
      color: #ff9900;
    }
    .toggle-btn {
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
    }

    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }
    .content {
      margin-left: 250px;
      min-height: 100vh;
      padding: 20px;
      transition: margin-left 0.3s;
    }
    .content.content-expanded {
      margin-left: 0 !important;
    }
    .glass-card {
      background: rgba(34, 34, 34, 0.9);
      border-radius: 20px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3), 0 0 0 1px rgba(255,204,102,0.1);
      border: 1px solid rgba(255,204,102,0.2);
      backdrop-filter: blur(10px);
      padding: 30px;
      margin-bottom: 30px;
      animation: fadeIn 0.6s ease-out;
    }
    .card-icon-circle {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: linear-gradient(135deg, #ffcc66 60%, #fffbe6 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 18px;
      box-shadow: 0 2px 12px rgba(255,204,102,0.15);
    }
     .form-control, .form-select, .form-check-input {
      background-color: #2e2e2e;
      color: #ffffff;
      border: 1px solid #444;
    }
    .form-control:focus, .form-select:focus, .form-check-input:focus {
      background-color: #2e2e2e;
      color: #ffffff;
      border-color: #ffcc00;
    }
    th {
      color: #ffcc00;
    }
    .dropdown-checklist {
      position: relative;
    }
    .dropdown-checklist .dropdown-menu {
  display: none;
  position: absolute;
  background-color: #2e2e2e;
  border: 1px solid #444;
  width: 100%;
  max-height: 200px;
  color: white;
  overflow-y: auto;
  z-index: 1050 !important; /* Updated z-index */
}
    .dropdown-checklist.show .dropdown-menu {
      display: block;
    }
    .dropdown-checklist .dropdown-toggle {
      width: 100%;
    }
   
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px);}
      to { opacity: 1; transform: translateY(0);}
    }

    /* Package Header Styles */
    .package-header {
      padding: 40px 0;
    }

    .package-icon {
      font-size: 4rem;
      color: #ffcc66;
      margin-bottom: 20px;
      display: block;
    }

    /* Card Header */
    .card-header-custom {
      border-bottom: 2px solid rgba(255,204,102,0.2);
      margin-bottom: 30px;
      padding-bottom: 15px;
    }

    /* Form Styles */
    .form-control-custom {
      background: rgba(46, 46, 46, 0.8);
      border: 2px solid rgba(255,204,102,0.3);
      color: #fff;
      border-radius: 12px;
      padding: 15px;
      font-size: 16px;
      transition: all 0.3s ease;
    }

    .form-control-custom:focus {
      background: rgba(46, 46, 46, 0.9);
      border-color: #ffcc66;
      box-shadow: 0 0 0 0.2rem rgba(255,204,102,0.25);
      color: #fff;
    }

    .form-floating > label {
      color: rgba(255,204,102,0.8);
      font-weight: 500;
    }

    .form-label-custom {
      color: #ffcc66;
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 10px;
      display: block;
    }

    /* Custom Dropdown */
    .dropdown-checklist-custom {
      position: relative;
    }

    .btn-dropdown-custom {
      width: 100%;
      background: rgba(46, 46, 46, 0.8);
      border: 2px solid rgba(255,204,102,0.3);
      color: #fff;
      border-radius: 12px;
      padding: 15px;
      text-align: left;
      font-size: 16px;
      transition: all 0.3s ease;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .btn-dropdown-custom:hover {
      background: rgba(46, 46, 46, 0.9);
      border-color: #ffcc66;
      color: #fff;
    }

    .dropdown-menu-custom {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: rgba(34, 34, 34, 0.95);
      border: 2px solid rgba(255,204,102,0.3);
      border-radius: 12px;
      margin-top: 5px;
      max-height: 300px;
      overflow-y: auto;
      z-index: 1000;
      display: none;
      backdrop-filter: blur(10px);
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }

    .dropdown-menu-custom.show {
      display: block;
      animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .search-container {
      position: relative;
      padding: 15px;
      border-bottom: 1px solid rgba(255,204,102,0.2);
    }

    .search-input {
      background: rgba(46, 46, 46, 0.8);
      border: 1px solid rgba(255,204,102,0.3);
      color: #fff;
      border-radius: 8px;
      padding: 10px 40px 10px 15px;
      width: 100%;
    }

    .search-input:focus {
      border-color: #ffcc66;
      box-shadow: none;
      background: rgba(46, 46, 46, 0.9);
      color: #fff;
    }

    .search-icon {
      position: absolute;
      right: 25px;
      top: 50%;
      transform: translateY(-50%);
      color: rgba(255,204,102,0.6);
    }

    .rides-list {
      max-height: 200px;
      overflow-y: auto;
      padding: 10px;
    }

    .ride-option {
      display: flex;
      align-items: center;
      padding: 12px 15px;
      margin: 5px 0;
      border-radius: 8px;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .ride-option:hover {
      background: rgba(255,204,102,0.1);
    }

    .ride-option input[type="checkbox"] {
      margin-right: 12px;
      transform: scale(1.2);
    }

    .ride-option label {
      color: #fff;
      cursor: pointer;
      margin: 0;
      display: flex;
      align-items: center;
      font-weight: 500;
    }

    .ride-icon {
      margin-right: 10px;
      color: #ffcc66;
      font-size: 1.1em;
    }
    /* Create Package Button */
    .btn-create-package {
      background: linear-gradient(135deg, #ffcc66 0%, #ff9900 100%);
      border: none;
      color: #000;
      font-weight: 700;
      font-size: 18px;
      padding: 15px 30px;
      border-radius: 12px;
      width: 100%;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .btn-create-package:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(255,204,102,0.4);
      color: #000;
    }

    .btn-shine {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }

    .btn-create-package:hover .btn-shine {
      left: 100%;
    }

    /* Package Cards Grid */
    .packages-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .package-card {
      background: rgba(46, 46, 46, 0.8);
      border: 2px solid rgba(255,204,102,0.3);
      border-radius: 15px;
      padding: 25px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .package-card:hover {
      transform: translateY(-5px);
      border-color: #ffcc66;
      box-shadow: 0 10px 30px rgba(255,204,102,0.2);
    }

    .package-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .package-name {
      color: #ffcc66;
      font-size: 1.4em;
      font-weight: 700;
      margin: 0;
    }

    .package-price {
      background: linear-gradient(135deg, #ffcc66, #ff9900);
      color: #000;
      padding: 8px 15px;
      border-radius: 20px;
      font-weight: 700;
      font-size: 1.1em;
    }

    .package-rides {
      margin: 15px 0;
    }

    .rides-title {
      color: rgba(255,204,102,0.8);
      font-size: 0.9em;
      margin-bottom: 10px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .rides-list-display {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .ride-tag {
      background: rgba(255,204,102,0.2);
      color: #ffcc66;
      padding: 5px 12px;
      border-radius: 15px;
      font-size: 0.85em;
      border: 1px solid rgba(255,204,102,0.3);
    }

    .package-actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }

    .btn-edit, .btn-delete {
      flex: 1;
      padding: 10px;
      border-radius: 8px;
      border: none;
      font-weight: 600;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .btn-edit {
      background: rgba(23, 162, 184, 0.8);
      color: #fff;
    }

    .btn-edit:hover {
      background: #17a2b8;
      transform: translateY(-2px);
    }

    .btn-delete {
      background: rgba(220, 53, 69, 0.8);
      color: #fff;
    }

    .btn-delete:hover {
      background: #dc3545;
      transform: translateY(-2px);
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: rgba(255,204,102,0.6);
    }

    .empty-state i {
      font-size: 4rem;
      margin-bottom: 20px;
      display: block;
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
        padding-top: 60px;
      }
      .content {
        margin-left: 0 !important;
      }
      .glass-card {
        padding: 20px;
        margin: 15px;
      }
      .package-header {
        padding: 20px 0;
      }
      .package-icon {
        font-size: 3rem;
      }
      .packages-grid {
        grid-template-columns: 1fr;
        gap: 15px;
      }
      .package-card {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="./search.html">Search Customer</a>
    <a href="#" id="reportsMenu" onclick="toggleReportsMenu()">Reports ▼</a>
    <div id="reportsSubMenu" style="display:none; margin-left:20px;">
      <a href="./sale-report.html">Sale Report</a>
      <a href="./return-report.html">Return Report</a>
      <a href="./individual-report.html">Individual Report</a>
    </div>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./add-ride.html">Add Ride</a>
    <a href="./setting.html">Settings</a>
  </div>
  <div class="content" id="content">
    <div class="container-fluid">

      <!-- Header Section -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="glass-card text-center">
            <div class="package-header">
              <i class="bi bi-box-seam package-icon"></i>
              <h1 class="display-6 text-warning mb-2">Package Management</h1>
              <p class="text-muted">Create and manage exciting ride packages for your customers</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Package Section -->
      <div class="row mb-4">
        <div class="col-lg-8 mx-auto">
          <div class="glass-card">
            <div class="card-header-custom">
              <h3 class="text-warning mb-0">
                <i class="bi bi-plus-circle-fill me-2"></i>Create New Package
              </h3>
            </div>

            <form class="package-form">
              <div class="row g-4">
                <!-- Package Name -->
                <div class="col-12">
                  <div class="form-floating">
                    <input type="text" id="dealName" class="form-control form-control-custom" placeholder="Enter package name">
                    <label for="dealName">
                      <i class="bi bi-tag-fill me-2"></i>Package Name
                    </label>
                  </div>
                </div>

                <!-- Select Rides -->
                <div class="col-md-6">
                  <label class="form-label-custom">
                    <i class="bi bi-collection-fill me-2"></i>Select Rides
                  </label>
                  <div class="dropdown-checklist-custom" id="rideDropdown">
                    <button type="button" class="btn btn-dropdown-custom" onclick="toggleDropdown()">
                      <span id="selectedRidesDisplay">Choose rides for this package</span>
                      <i class="bi bi-chevron-down ms-2"></i>
                    </button>
                    <div class="dropdown-menu-custom">
                      <div class="search-container">
                        <input type="text" class="form-control search-input" id="rideSearch" placeholder="Search rides..." onkeyup="filterRides()">
                        <i class="bi bi-search search-icon"></i>
                      </div>
                      <div class="rides-list" id="rideList">
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride1" value="Roller Coaster" onchange="updateSelected()">
                          <label class="form-check-label" for="ride1">
                            <i class="bi bi-lightning-charge-fill ride-icon"></i>
                            Roller Coaster
                          </label>
                        </div>
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride2" value="Ferris Wheel" onchange="updateSelected()">
                          <label class="form-check-label" for="ride2">
                            <i class="bi bi-circle-fill ride-icon"></i>
                            Ferris Wheel
                          </label>
                        </div>
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride3" value="Water Slide" onchange="updateSelected()">
                          <label class="form-check-label" for="ride3">
                            <i class="bi bi-water ride-icon"></i>
                            Water Slide
                          </label>
                        </div>
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride4" value="Haunted House" onchange="updateSelected()">
                          <label class="form-check-label" for="ride4">
                            <i class="bi bi-house-fill ride-icon"></i>
                            Haunted House
                          </label>
                        </div>
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride5" value="Bumper Cars" onchange="updateSelected()">
                          <label class="form-check-label" for="ride5">
                            <i class="bi bi-car-front-fill ride-icon"></i>
                            Bumper Cars
                          </label>
                        </div>
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride6" value="Drop Tower" onchange="updateSelected()">
                          <label class="form-check-label" for="ride6">
                            <i class="bi bi-arrow-down-circle-fill ride-icon"></i>
                            Drop Tower
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Package Price -->
                <div class="col-md-6">
                  <div class="form-floating">
                    <input type="number" id="ridePrice" class="form-control form-control-custom" placeholder="0" min="0">
                    <label for="ridePrice">
                      <i class="bi bi-currency-dollar me-2"></i>Package Price (PKR)
                    </label>
                  </div>
                </div>

                <!-- Submit Button -->
                <div class="col-12">
                  <button type="button" onclick="addPackage()" class="btn btn-create-package">
                    <i class="bi bi-plus-lg me-2"></i>Create Package
                    <div class="btn-shine"></div>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Package List Section -->
      <div class="row">
        <div class="col-12">
          <div class="glass-card">
            <div class="card-header-custom">
              <h3 class="text-warning mb-0">
                <i class="bi bi-list-ul me-2"></i>Active Packages
              </h3>
            </div>

            <div class="packages-grid" id="packagesGrid">
              <!-- Packages will be displayed here -->
            </div>

            <!-- Fallback Table for Mobile -->
            <div class="table-responsive d-none" id="packageTableContainer">
              <table class="table table-dark table-hover">
                <thead>
                  <tr>
                    <th scope="col">
                      <i class="bi bi-tag-fill me-2"></i>Package Name
                    </th>
                    <th scope="col">
                      <i class="bi bi-collection-fill me-2"></i>Rides
                    </th>
                    <th scope="col">
                      <i class="bi bi-currency-dollar me-2"></i>Price
                    </th>
                    <th scope="col">
                      <i class="bi bi-gear-fill me-2"></i>Actions
                    </th>
                  </tr>
                </thead>
                <tbody id="packageTable"></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
  <script>
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');

      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      toggleBtn.classList.toggle('active');

      // Toggle icon
      toggleBtn.innerText = toggleBtn.innerText === '☰' ? '✖' : '☰';
    }
    function toggleReportsMenu() {
      const subMenu = document.getElementById('reportsSubMenu');
      subMenu.style.display = subMenu.style.display === 'none' ? 'block' : 'none';
    }
   
  </script>

 <script>
  function toggleDropdown() {
    document.getElementById("rideDropdown").classList.toggle("show");
  }

  function updateSelected() {
    const checkboxes = document.querySelectorAll("#rideDropdown input[type='checkbox']");
    const selectedDisplay = document.getElementById("selectedRidesDisplay");
    const selected = Array.from(checkboxes)
      .filter(cb => cb.checked)
      .map(cb => `<span class='selected-ride'>${cb.value}</span>`);
    selectedDisplay.innerHTML = selected.length > 0 ? selected.join(' ') : "Select Rides";
  }

  function filterRides() {
    const input = document.getElementById("rideSearch");
    const filter = input.value.toLowerCase();
    const rideList = document.getElementById("rideList");
    const items = rideList.getElementsByClassName("form-check");

    for (let i = 0; i < items.length; i++) {
      const label = items[i].getElementsByTagName("label")[0];
      const text = label.textContent || label.innerText;
      items[i].style.display = text.toLowerCase().includes(filter) ? "" : "none";
    }
  }

  function addPackage() {
    const dealName = document.getElementById("dealName").value.trim();
    const checkboxes = document.querySelectorAll(".ride-option input[type='checkbox']");
    const price = document.getElementById("ridePrice").value;

    const selectedRides = Array.from(checkboxes)
      .filter(cb => cb.checked)
      .map(cb => cb.value);

    if (!dealName || selectedRides.length === 0 || !price) {
      alert("Please enter package name, select at least one ride, and enter the price.");
      return;
    }

    if (price <= 0) {
      alert('Please enter a valid price.');
      return;
    }

    // Create package object
    const newPackage = {
      id: Date.now(),
      name: dealName,
      rides: selectedRides,
      price: parseInt(price)
    };

    packages.push(newPackage);
    displayPackages();

    // Reset form
    document.getElementById("dealName").value = "";
    checkboxes.forEach(cb => cb.checked = false);
    document.getElementById("ridePrice").value = "";
    document.querySelector(".dropdown-menu-custom").classList.remove("show");
    updateSelected();

    // Show success message
    showSuccessMessage('Package created successfully!');
  }

  function displayPackages() {
    const packagesGrid = document.getElementById('packagesGrid');

    if (packages.length === 0) {
      packagesGrid.innerHTML = `
        <div class="empty-state">
          <i class="bi bi-box-seam"></i>
          <h4>No packages created yet</h4>
          <p>Create your first package to get started!</p>
        </div>
      `;
      return;
    }

    packagesGrid.innerHTML = packages.map(pkg => `
      <div class="package-card" data-id="${pkg.id}">
        <div class="package-card-header">
          <h4 class="package-name">${pkg.name}</h4>
          <div class="package-price">PKR ${pkg.price}</div>
        </div>
        <div class="package-rides">
          <div class="rides-title">Included Rides</div>
          <div class="rides-list-display">
            ${pkg.rides.map(ride => `<span class="ride-tag">${ride}</span>`).join('')}
          </div>
        </div>
        <div class="package-actions">
          <button class="btn-edit" onclick="editPackage(${pkg.id})">
            <i class="bi bi-pencil-fill me-1"></i>Edit
          </button>
          <button class="btn-delete" onclick="deletePackage(${pkg.id})">
            <i class="bi bi-trash-fill me-1"></i>Delete
          </button>
        </div>
      </div>
    `).join('');
  }

  function editPackage(id) {
    const pkg = packages.find(p => p.id === id);
    if (!pkg) return;

    document.getElementById('dealName').value = pkg.name;
    document.getElementById('ridePrice').value = pkg.price;

    // Clear all checkboxes first
    const checkboxes = document.querySelectorAll('.ride-option input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = false);

    // Check the rides in this package
    pkg.rides.forEach(rideName => {
      const checkbox = Array.from(checkboxes).find(cb => cb.value === rideName);
      if (checkbox) checkbox.checked = true;
    });

    updateSelected();

    // Remove the package (will be re-added when form is submitted)
    packages = packages.filter(p => p.id !== id);
    displayPackages();

    // Scroll to form
    document.querySelector('.package-form').scrollIntoView({ behavior: 'smooth' });
  }

  function deletePackage(id) {
    if (confirm('Are you sure you want to delete this package?')) {
      packages = packages.filter(p => p.id !== id);
      displayPackages();
      showSuccessMessage('Package deleted successfully!');
    }
  }

  function showSuccessMessage(message) {
    // Create and show a temporary success message
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      padding: 15px 25px;
      border-radius: 10px;
      z-index: 9999;
      font-weight: 600;
      box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
    `;
    successDiv.innerHTML = `<i class="bi bi-check-circle-fill me-2"></i>${message}`;

    document.body.appendChild(successDiv);

    setTimeout(() => successDiv.remove(), 3000);
  }

  // Global packages array
  let packages = [];

  document.addEventListener("click", function(event) {
    const dropdown = document.querySelector('.dropdown-checklist-custom');
    if (!dropdown.contains(event.target)) {
      document.querySelector('.dropdown-menu-custom').classList.remove("show");
    }
  });

  // Initialize the page
  document.addEventListener('DOMContentLoaded', function() {
    displayPackages();
  });
</script>
</body>
</html>