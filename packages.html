<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Serach - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      background-color: #1c1c1c;
      color: #f8f9fa;
      min-height: 100vh;
      overflow-x: hidden;
    }
    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }
    .sidebar.sidebar-collapsed {
      width: 0 !important;
      padding: 0 !important;
      overflow: hidden;
    }
    .sidebar a, .sidebar #reportsMenu {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
      transition: background 0.3s, color 0.3s, padding-left 0.3s;
    }
    .sidebar a:hover, .sidebar #reportsMenu:hover {
      background-color: #444;
      color: #ff9900;
      padding-left: 25px;
    }
    #reportsSubMenu {
      transition: max-height 0.4s, opacity 0.4s;
      overflow: hidden;
    }
    #reportsSubMenu[style*="display: block"] {
      opacity: 1;
      max-height: 200px;
    }
    #reportsSubMenu[style*="display: none"] {
      opacity: 0;
      max-height: 0;
    }
    #reportsSubMenu a {
      color: #ffcc66;
      padding: 10px 15px;
      display: block;
      text-decoration: none;
      font-size: 15px;
      transition: background 0.3s, color 0.3s;
    }
    #reportsSubMenu a:hover {
      background-color: #444;
      color: #ff9900;
    }
    .toggle-btn {
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
    }

    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }
    .content {
      margin-left: 250px;
      min-height: 100vh;
      padding: 20px;
      transition: margin-left 0.3s;
    }
    .content.content-expanded {
      margin-left: 0 !important;
    }
    /* Page Header */
    .page-header {
      text-align: center;
      margin-bottom: 40px;
      padding: 30px 0;
    }

    .page-header h1 {
      color: #ffcc66;
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 10px;
    }

    .page-header p {
      color: #aaa;
      font-size: 1.1rem;
    }

    /* Form Card */
    .form-card {
      background: #2a2a2a;
      border-radius: 15px;
      border: 2px solid #ffcc66;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      overflow: hidden;
      margin-bottom: 30px;
    }

    .form-header {
      background: linear-gradient(135deg, #ffcc66, #ff9900);
      color: #000;
      padding: 20px 25px;
      font-weight: 700;
    }

    .form-header h3 {
      margin: 0;
      font-size: 1.3rem;
    }

    .form-body {
      padding: 30px 25px;
    }

    /* Input Groups */
    .input-group-custom {
      margin-bottom: 25px;
    }

    .input-group-custom label {
      display: block;
      color: #ffcc66;
      font-weight: 600;
      margin-bottom: 8px;
      font-size: 0.95rem;
    }

    .form-input {
      width: 100%;
      padding: 12px 15px;
      background: #1a1a1a;
      border: 2px solid #444;
      border-radius: 8px;
      color: #fff;
      font-size: 16px;
      transition: all 0.3s ease;
    }

    .form-input:focus {
      outline: none;
      border-color: #ffcc66;
      background: #222;
    }

    /* Rides Selector */
    .rides-selector {
      background: #1a1a1a;
      border: 2px solid #444;
      border-radius: 8px;
      padding: 15px;
    }

    .search-box {
      position: relative;
      margin-bottom: 15px;
    }

    .search-box i {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #888;
    }

    .search-box input {
      width: 100%;
      padding: 10px 10px 10px 35px;
      background: #333;
      border: 1px solid #555;
      border-radius: 6px;
      color: #fff;
      font-size: 14px;
    }

    .search-box input:focus {
      outline: none;
      border-color: #ffcc66;
    }

    /* Rides Grid */
    .rides-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 10px;
      max-height: 300px;
      overflow-y: auto;
      padding: 5px;
    }

    .ride-item {
      background: #333;
      border: 2px solid transparent;
      border-radius: 8px;
      padding: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .ride-item:hover {
      background: #444;
      border-color: #ffcc66;
    }

    .ride-item.selected {
      background: #ffcc66;
      color: #000;
      border-color: #ff9900;
    }

    .ride-item input[type="checkbox"] {
      display: none;
    }

    .ride-item .ride-icon {
      font-size: 1.2em;
      width: 20px;
      text-align: center;
    }

    .ride-item .ride-name {
      font-weight: 500;
      font-size: 0.9rem;
    }

    /* Selected Rides */
    .selected-rides {
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid #555;
    }

    .selected-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }

    .selected-header span {
      color: #ffcc66;
      font-weight: 600;
      font-size: 0.9rem;
    }

    .clear-all {
      background: none;
      border: none;
      color: #ff6b6b;
      font-size: 0.8rem;
      cursor: pointer;
      text-decoration: underline;
    }

    .selected-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .selected-tag {
      background: #ffcc66;
      color: #000;
      padding: 5px 10px;
      border-radius: 15px;
      font-size: 0.8rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .selected-tag .remove {
      cursor: pointer;
      font-weight: bold;
      margin-left: 5px;
    }

    /* Create Button */
    .create-btn {
      width: 100%;
      background: linear-gradient(135deg, #ffcc66, #ff9900);
      border: none;
      color: #000;
      padding: 15px;
      border-radius: 8px;
      font-size: 1.1rem;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .create-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255,204,102,0.4);
    }
    .card-icon-circle {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: linear-gradient(135deg, #ffcc66 60%, #fffbe6 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 18px;
      box-shadow: 0 2px 12px rgba(255,204,102,0.15);
    }
     .form-control, .form-select, .form-check-input {
      background-color: #2e2e2e;
      color: #ffffff;
      border: 1px solid #444;
    }
    .form-control:focus, .form-select:focus, .form-check-input:focus {
      background-color: #2e2e2e;
      color: #ffffff;
      border-color: #ffcc00;
    }
    th {
      color: #ffcc00;
    }
    .dropdown-checklist {
      position: relative;
    }
    .dropdown-checklist .dropdown-menu {
  display: none;
  position: absolute;
  background-color: #2e2e2e;
  border: 1px solid #444;
  width: 100%;
  max-height: 200px;
  color: white;
  overflow-y: auto;
  z-index: 1050 !important; /* Updated z-index */
}
    .dropdown-checklist.show .dropdown-menu {
      display: block;
    }
    .dropdown-checklist .dropdown-toggle {
      width: 100%;
    }
   
    /* Packages Container */
    .packages-container {
      background: #2a2a2a;
      border-radius: 15px;
      border: 2px solid #ffcc66;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      overflow: hidden;
      height: fit-content;
    }

    .packages-header {
      background: linear-gradient(135deg, #ffcc66, #ff9900);
      color: #000;
      padding: 20px 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .packages-header h3 {
      margin: 0;
      font-size: 1.3rem;
      font-weight: 700;
    }

    .packages-count {
      background: rgba(0,0,0,0.2);
      padding: 5px 12px;
      border-radius: 15px;
      font-size: 0.9rem;
      font-weight: 600;
    }

    .packages-list {
      padding: 20px;
      max-height: 600px;
      overflow-y: auto;
    }

    /* Package Item */
    .package-item {
      background: #1a1a1a;
      border: 2px solid #444;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 15px;
      transition: all 0.3s ease;
    }

    .package-item:hover {
      border-color: #ffcc66;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(255,204,102,0.2);
    }

    .package-header-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .package-name {
      color: #ffcc66;
      font-size: 1.3rem;
      font-weight: 700;
      margin: 0;
    }

    .package-price {
      background: linear-gradient(135deg, #ffcc66, #ff9900);
      color: #000;
      padding: 8px 15px;
      border-radius: 20px;
      font-weight: 700;
      font-size: 1.1rem;
    }

    .package-rides {
      margin-bottom: 15px;
    }

    .rides-label {
      color: #aaa;
      font-size: 0.9rem;
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .rides-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .ride-tag {
      background: #333;
      color: #ffcc66;
      padding: 5px 12px;
      border-radius: 15px;
      font-size: 0.85rem;
      border: 1px solid #555;
    }

    .package-actions {
      display: flex;
      gap: 10px;
    }

    .btn-edit, .btn-delete {
      flex: 1;
      padding: 10px 15px;
      border: none;
      border-radius: 6px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.9rem;
    }

    .btn-edit {
      background: #17a2b8;
      color: #fff;
    }

    .btn-edit:hover {
      background: #138496;
      transform: translateY(-1px);
    }

    .btn-delete {
      background: #dc3545;
      color: #fff;
    }

    .btn-delete:hover {
      background: #c82333;
      transform: translateY(-1px);
    }

    /* Empty State */
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-state i {
      font-size: 4rem;
      margin-bottom: 20px;
      display: block;
      color: #555;
    }

    .empty-state h4 {
      color: #888;
      margin-bottom: 10px;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px);}
      to { opacity: 1; transform: translateY(0);}
    }
    /* Create Package Button */
    .btn-create-package {
      background: linear-gradient(135deg, #ffcc66 0%, #ff9900 100%);
      border: none;
      color: #000;
      font-weight: 700;
      font-size: 18px;
      padding: 15px 30px;
      border-radius: 12px;
      width: 100%;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .btn-create-package:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(255,204,102,0.4);
      color: #000;
    }

    .btn-shine {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }

    .btn-create-package:hover .btn-shine {
      left: 100%;
    }

    /* Package Cards Grid */
    .packages-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .package-card {
      background: rgba(46, 46, 46, 0.8);
      border: 2px solid rgba(255,204,102,0.3);
      border-radius: 15px;
      padding: 25px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .package-card:hover {
      transform: translateY(-5px);
      border-color: #ffcc66;
      box-shadow: 0 10px 30px rgba(255,204,102,0.2);
    }

    .package-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .package-name {
      color: #ffcc66;
      font-size: 1.4em;
      font-weight: 700;
      margin: 0;
    }

    .package-price {
      background: linear-gradient(135deg, #ffcc66, #ff9900);
      color: #000;
      padding: 8px 15px;
      border-radius: 20px;
      font-weight: 700;
      font-size: 1.1em;
    }

    .package-rides {
      margin: 15px 0;
    }

    .rides-title {
      color: rgba(255,204,102,0.8);
      font-size: 0.9em;
      margin-bottom: 10px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .rides-list-display {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .ride-tag {
      background: rgba(255,204,102,0.2);
      color: #ffcc66;
      padding: 5px 12px;
      border-radius: 15px;
      font-size: 0.85em;
      border: 1px solid rgba(255,204,102,0.3);
    }

    .package-actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }

    .btn-edit, .btn-delete {
      flex: 1;
      padding: 10px;
      border-radius: 8px;
      border: none;
      font-weight: 600;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .btn-edit {
      background: rgba(23, 162, 184, 0.8);
      color: #fff;
    }

    .btn-edit:hover {
      background: #17a2b8;
      transform: translateY(-2px);
    }

    .btn-delete {
      background: rgba(220, 53, 69, 0.8);
      color: #fff;
    }

    .btn-delete:hover {
      background: #dc3545;
      transform: translateY(-2px);
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: rgba(255,204,102,0.6);
    }

    .empty-state i {
      font-size: 4rem;
      margin-bottom: 20px;
      display: block;
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
        padding-top: 60px;
      }
      .content {
        margin-left: 0 !important;
      }
      .glass-card {
        padding: 20px;
        margin: 15px;
      }
      .package-header {
        padding: 20px 0;
      }
      .package-icon {
        font-size: 3rem;
      }
      .packages-grid {
        grid-template-columns: 1fr;
        gap: 15px;
      }
      .package-card {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="./search.html">Search Customer</a>
    <a href="#" id="reportsMenu" onclick="toggleReportsMenu()">Reports ▼</a>
    <div id="reportsSubMenu" style="display:none; margin-left:20px;">
      <a href="./sale-report.html">Sale Report</a>
      <a href="./return-report.html">Return Report</a>
      <a href="./individual-report.html">Individual Report</a>
    </div>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./add-ride.html">Add Ride</a>
    <a href="./setting.html">Settings</a>
  </div>
  <div class="content" id="content">
    <div class="container-fluid">

      <!-- Page Header -->
      <div class="page-header">
        <h1><i class="bi bi-box-seam me-3"></i>Package Management</h1>
        <p>Create and manage ride packages for your amusement park</p>
      </div>

      <div class="row">
        <!-- Create Package Form -->
        <div class="col-lg-5">
          <div class="form-card">
            <div class="form-header">
              <h3><i class="bi bi-plus-circle me-2"></i>Create New Package</h3>
            </div>

            <div class="form-body">
              <!-- Package Name -->
              <div class="input-group-custom">
                <label>Package Name</label>
                <input type="text" id="dealName" placeholder="Enter package name" class="form-input">
              </div>

              <!-- Rides Selection -->
              <div class="input-group-custom">
                <label>Select Rides</label>
                <div class="rides-selector">
                  <div class="search-box">
                    <i class="bi bi-search"></i>
                    <input type="text" id="rideSearch" placeholder="Search rides..." onkeyup="filterRides()">
                  </div>

                  <div class="rides-grid" id="ridesGrid">
                    <!-- Rides will be populated by JavaScript -->
                  </div>

                  <div class="selected-rides" id="selectedRidesContainer" style="display: none;">
                    <div class="selected-header">
                      <span>Selected Rides:</span>
                      <button type="button" class="clear-all" onclick="clearAllRides()">Clear All</button>
                    </div>
                    <div class="selected-list" id="selectedRidesList"></div>
                  </div>
                </div>
              </div>

              <!-- Package Price -->
              <div class="input-group-custom">
                <label>Package Price (PKR)</label>
                <input type="number" id="ridePrice" placeholder="0" min="0" class="form-input">
              </div>

              <!-- Create Button -->
              <button type="button" onclick="addPackage()" class="create-btn">
                <i class="bi bi-plus-lg me-2"></i>Create Package
              </button>
            </div>
          </div>
        </div>

        <!-- Packages List -->
        <div class="col-lg-7">
          <div class="packages-container">
            <div class="packages-header">
              <h3><i class="bi bi-list-ul me-2"></i>Active Packages</h3>
              <div class="packages-count">
                <span id="packagesCount">0 packages</span>
              </div>
            </div>

            <div class="packages-list" id="packagesList">
              <!-- Packages will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>


  <script>
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');

      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      toggleBtn.classList.toggle('active');

      // Toggle icon
      toggleBtn.innerText = toggleBtn.innerText === '☰' ? '✖' : '☰';
    }
    function toggleReportsMenu() {
      const subMenu = document.getElementById('reportsSubMenu');
      subMenu.style.display = subMenu.style.display === 'none' ? 'block' : 'none';
    }
   
  </script>

 <script>
  // Available rides with more options
  const availableRides = [
    { name: 'Roller Coaster', icon: 'bi-lightning-charge-fill', category: 'Thrill' },
    { name: 'Ferris Wheel', icon: 'bi-circle-fill', category: 'Family' },
    { name: 'Water Slide', icon: 'bi-water', category: 'Water' },
    { name: 'Haunted House', icon: 'bi-house-fill', category: 'Thrill' },
    { name: 'Bumper Cars', icon: 'bi-car-front-fill', category: 'Family' },
    { name: 'Drop Tower', icon: 'bi-arrow-down-circle-fill', category: 'Thrill' },
    { name: 'Carousel', icon: 'bi-arrow-clockwise', category: 'Kids' },
    { name: 'Log Flume', icon: 'bi-water', category: 'Water' },
    { name: 'Spinning Cups', icon: 'bi-cup-fill', category: 'Family' },
    { name: 'Pirate Ship', icon: 'bi-ship', category: 'Thrill' },
    { name: 'Mini Golf', icon: 'bi-flag-fill', category: 'Family' },
    { name: 'Go Karts', icon: 'bi-speedometer2', category: 'Racing' },
    { name: 'Zip Line', icon: 'bi-arrow-right', category: 'Adventure' },
    { name: 'Rock Climbing', icon: 'bi-triangle-fill', category: 'Adventure' },
    { name: 'Laser Tag', icon: 'bi-bullseye', category: 'Action' },
    { name: 'Trampoline Park', icon: 'bi-arrow-up-circle', category: 'Action' },
    { name: 'Wave Pool', icon: 'bi-water', category: 'Water' },
    { name: 'Sky Coaster', icon: 'bi-airplane', category: 'Thrill' },
    { name: 'Arcade Games', icon: 'bi-controller', category: 'Indoor' },
    { name: 'Food Court', icon: 'bi-cup-straw', category: 'Dining' }
  ];

  let selectedRides = [];
  let packages = [];

  function initializeRides() {
    const ridesGrid = document.getElementById('ridesGrid');
    ridesGrid.innerHTML = availableRides.map(ride => `
      <div class="ride-item" onclick="toggleRide('${ride.name}')">
        <input type="checkbox" id="ride-${ride.name.replace(/\s+/g, '-')}" value="${ride.name}">
        <i class="bi ${ride.icon} ride-icon"></i>
        <span class="ride-name">${ride.name}</span>
      </div>
    `).join('');
  }

  function toggleRide(rideName) {
    const rideItem = event.currentTarget;
    const checkbox = rideItem.querySelector('input[type="checkbox"]');

    if (selectedRides.includes(rideName)) {
      selectedRides = selectedRides.filter(r => r !== rideName);
      rideItem.classList.remove('selected');
      checkbox.checked = false;
    } else {
      selectedRides.push(rideName);
      rideItem.classList.add('selected');
      checkbox.checked = true;
    }

    updateSelectedDisplay();
  }

  function updateSelectedDisplay() {
    const container = document.getElementById('selectedRidesContainer');
    const list = document.getElementById('selectedRidesList');

    if (selectedRides.length === 0) {
      container.style.display = 'none';
    } else {
      container.style.display = 'block';
      list.innerHTML = selectedRides.map(ride => `
        <span class="selected-tag">
          ${ride}
          <span class="remove" onclick="removeRide('${ride}')">&times;</span>
        </span>
      `).join('');
    }
  }

  function removeRide(rideName) {
    selectedRides = selectedRides.filter(r => r !== rideName);
    const rideItem = document.querySelector(`input[value="${rideName}"]`).closest('.ride-item');
    rideItem.classList.remove('selected');
    rideItem.querySelector('input[type="checkbox"]').checked = false;
    updateSelectedDisplay();
  }

  function clearAllRides() {
    selectedRides = [];
    document.querySelectorAll('.ride-item').forEach(item => {
      item.classList.remove('selected');
      item.querySelector('input[type="checkbox"]').checked = false;
    });
    updateSelectedDisplay();
  }

  function filterRides() {
    const searchTerm = document.getElementById('rideSearch').value.toLowerCase();
    document.querySelectorAll('.ride-item').forEach(item => {
      const rideName = item.querySelector('.ride-name').textContent.toLowerCase();
      item.style.display = rideName.includes(searchTerm) ? 'flex' : 'none';
    });
  }

  function addPackage() {
    const dealName = document.getElementById("dealName").value.trim();
    const price = document.getElementById("ridePrice").value;

    if (!dealName || selectedRides.length === 0 || !price) {
      alert("Please enter package name, select at least one ride, and enter the price.");
      return;
    }

    if (price <= 0) {
      alert('Please enter a valid price.');
      return;
    }

    // Create package object
    const newPackage = {
      id: Date.now(),
      name: dealName,
      rides: [...selectedRides],
      price: parseInt(price)
    };

    packages.push(newPackage);
    displayPackages();
    clearForm();
    showSuccessMessage('Package created successfully!');
  }

  function clearForm() {
    document.getElementById("dealName").value = "";
    document.getElementById("ridePrice").value = "";
    clearAllRides();
  }

  function displayPackages() {
    const packagesList = document.getElementById('packagesList');
    const packagesCount = document.getElementById('packagesCount');

    packagesCount.textContent = `${packages.length} package${packages.length !== 1 ? 's' : ''}`;

    if (packages.length === 0) {
      packagesList.innerHTML = `
        <div class="empty-state">
          <i class="bi bi-box-seam"></i>
          <h4>No packages created yet</h4>
          <p>Create your first package to get started!</p>
        </div>
      `;
      return;
    }

    packagesList.innerHTML = packages.map(pkg => `
      <div class="package-item">
        <div class="package-header-item">
          <h4 class="package-name">${pkg.name}</h4>
          <div class="package-price">PKR ${pkg.price.toLocaleString()}</div>
        </div>
        <div class="package-rides">
          <div class="rides-label">Included Rides (${pkg.rides.length})</div>
          <div class="rides-tags">
            ${pkg.rides.map(ride => `<span class="ride-tag">${ride}</span>`).join('')}
          </div>
        </div>
        <div class="package-actions">
          <button class="btn-edit" onclick="editPackage(${pkg.id})">
            <i class="bi bi-pencil-fill me-1"></i>Edit
          </button>
          <button class="btn-delete" onclick="deletePackage(${pkg.id})">
            <i class="bi bi-trash-fill me-1"></i>Delete
          </button>
        </div>
      </div>
    `).join('');
  }

  function editPackage(id) {
    const pkg = packages.find(p => p.id === id);
    if (!pkg) return;

    // Fill form with package data
    document.getElementById('dealName').value = pkg.name;
    document.getElementById('ridePrice').value = pkg.price;

    // Clear current selection
    clearAllRides();

    // Select the rides from this package
    pkg.rides.forEach(rideName => {
      if (availableRides.find(r => r.name === rideName)) {
        selectedRides.push(rideName);
        const rideItem = document.querySelector(`input[value="${rideName}"]`).closest('.ride-item');
        rideItem.classList.add('selected');
        rideItem.querySelector('input[type="checkbox"]').checked = true;
      }
    });

    updateSelectedDisplay();

    // Remove the package (will be re-added when form is submitted)
    packages = packages.filter(p => p.id !== id);
    displayPackages();

    // Scroll to form
    document.querySelector('.form-card').scrollIntoView({ behavior: 'smooth' });
  }

  function deletePackage(id) {
    if (confirm('Are you sure you want to delete this package?')) {
      packages = packages.filter(p => p.id !== id);
      displayPackages();
      showSuccessMessage('Package deleted successfully!');
    }
  }

  function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      padding: 15px 25px;
      border-radius: 10px;
      z-index: 9999;
      font-weight: 600;
      box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
    `;
    successDiv.innerHTML = `<i class="bi bi-check-circle-fill me-2"></i>${message}`;

    document.body.appendChild(successDiv);
    setTimeout(() => successDiv.remove(), 3000);
  }

  // Initialize the page
  document.addEventListener('DOMContentLoaded', function() {
    initializeRides();
    displayPackages();
  });
</script>
</body>
</html>