<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Serach - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      background-color: #1c1c1c;
      color: #f8f9fa;
      min-height: 100vh;
      overflow-x: hidden;
    }
    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }
    .sidebar.sidebar-collapsed {
      width: 0 !important;
      padding: 0 !important;
      overflow: hidden;
    }
    .sidebar a, .sidebar #reportsMenu {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
      transition: background 0.3s, color 0.3s, padding-left 0.3s;
    }
    .sidebar a:hover, .sidebar #reportsMenu:hover {
      background-color: #444;
      color: #ff9900;
      padding-left: 25px;
    }
    #reportsSubMenu {
      transition: max-height 0.4s, opacity 0.4s;
      overflow: hidden;
    }
    #reportsSubMenu[style*="display: block"] {
      opacity: 1;
      max-height: 200px;
    }
    #reportsSubMenu[style*="display: none"] {
      opacity: 0;
      max-height: 0;
    }
    #reportsSubMenu a {
      color: #ffcc66;
      padding: 10px 15px;
      display: block;
      text-decoration: none;
      font-size: 15px;
      transition: background 0.3s, color 0.3s;
    }
    #reportsSubMenu a:hover {
      background-color: #444;
      color: #ff9900;
    }
    .toggle-btn {
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
    }

    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }
    .content {
      margin-left: 250px;
      min-height: 100vh;
      padding: 20px;
      transition: margin-left 0.3s;
    }
    .content.content-expanded {
      margin-left: 0 !important;
    }
    .glass-card {
      background: rgba(34, 34, 34, 0.7);
      border-radius: 22px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.22), 0 0 0 4px rgba(255,204,102,0.10);
      border: 1.5px solid rgba(255,204,102,0.18);
      
      padding: 32px 28px 28px 28px;
      animation: fadeIn 0.7s;
      position: relative;
      max-width: 900px;
      width: 100%;
      margin: 40px auto;
    }
    .card-icon-circle {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: linear-gradient(135deg, #ffcc66 60%, #fffbe6 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 18px;
      box-shadow: 0 2px 12px rgba(255,204,102,0.15);
    }
     .form-control, .form-select, .form-check-input {
      background-color: #2e2e2e;
      color: #ffffff;
      border: 1px solid #444;
    }
    .form-control:focus, .form-select:focus, .form-check-input:focus {
      background-color: #2e2e2e;
      color: #ffffff;
      border-color: #ffcc00;
    }
    th {
      color: #ffcc00;
    }
    .dropdown-checklist {
      position: relative;
    }
    .dropdown-checklist .dropdown-menu {
  display: none;
  position: absolute;
  background-color: #2e2e2e;
  border: 1px solid #444;
  width: 100%;
  max-height: 200px;
  color: white;
  overflow-y: auto;
  z-index: 1050 !important; /* Updated z-index */
}
    .dropdown-checklist.show .dropdown-menu {
      display: block;
    }
    .dropdown-checklist .dropdown-toggle {
      width: 100%;
    }
   
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px);}
      to { opacity: 1; transform: translateY(0);}
    }
    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
        padding-top: 60px;
      }

      .content {
        margin-left: 0 !important;
      }
      .glass-card {
        padding: 18px 8px 18px 8px;
      }
    }
  </style>
</head>
<body>
  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="./search.html">Search Customer</a>
    <a href="#" id="reportsMenu" onclick="toggleReportsMenu()">Reports ▼</a>
    <div id="reportsSubMenu" style="display:none; margin-left:20px;">
      <a href="./sale-report.html">Sale Report</a>
      <a href="./return-report.html">Return Report</a>
      <a href="./individual-report.html">Individual Report</a>
    </div>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./add-ride.html">Add Ride</a>
    <a href="./setting.html">Settings</a>
  </div>
  <div class="content" id="content">
    <div class="container-fluid">

      <!-- Header Section -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="glass-card text-center">
            <div class="package-header">
              <i class="bi bi-box-seam package-icon"></i>
              <h1 class="display-6 text-warning mb-2">Package Management</h1>
              <p class="text-muted">Create and manage exciting ride packages for your customers</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Package Section -->
      <div class="row mb-4">
        <div class="col-lg-8 mx-auto">
          <div class="glass-card">
            <div class="card-header-custom">
              <h3 class="text-warning mb-0">
                <i class="bi bi-plus-circle-fill me-2"></i>Create New Package
              </h3>
            </div>

            <form class="package-form">
              <div class="row g-4">
                <!-- Package Name -->
                <div class="col-12">
                  <div class="form-floating">
                    <input type="text" id="dealName" class="form-control form-control-custom" placeholder="Enter package name">
                    <label for="dealName">
                      <i class="bi bi-tag-fill me-2"></i>Package Name
                    </label>
                  </div>
                </div>

                <!-- Select Rides -->
                <div class="col-md-6">
                  <label class="form-label-custom">
                    <i class="bi bi-collection-fill me-2"></i>Select Rides
                  </label>
                  <div class="dropdown-checklist-custom" id="rideDropdown">
                    <button type="button" class="btn btn-dropdown-custom" onclick="toggleDropdown()">
                      <span id="selectedRidesDisplay">Choose rides for this package</span>
                      <i class="bi bi-chevron-down ms-2"></i>
                    </button>
                    <div class="dropdown-menu-custom">
                      <div class="search-container">
                        <input type="text" class="form-control search-input" id="rideSearch" placeholder="Search rides..." onkeyup="filterRides()">
                        <i class="bi bi-search search-icon"></i>
                      </div>
                      <div class="rides-list" id="rideList">
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride1" value="Roller Coaster" onchange="updateSelected()">
                          <label class="form-check-label" for="ride1">
                            <i class="bi bi-lightning-charge-fill ride-icon"></i>
                            Roller Coaster
                          </label>
                        </div>
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride2" value="Ferris Wheel" onchange="updateSelected()">
                          <label class="form-check-label" for="ride2">
                            <i class="bi bi-circle-fill ride-icon"></i>
                            Ferris Wheel
                          </label>
                        </div>
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride3" value="Water Slide" onchange="updateSelected()">
                          <label class="form-check-label" for="ride3">
                            <i class="bi bi-water ride-icon"></i>
                            Water Slide
                          </label>
                        </div>
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride4" value="Haunted House" onchange="updateSelected()">
                          <label class="form-check-label" for="ride4">
                            <i class="bi bi-house-fill ride-icon"></i>
                            Haunted House
                          </label>
                        </div>
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride5" value="Bumper Cars" onchange="updateSelected()">
                          <label class="form-check-label" for="ride5">
                            <i class="bi bi-car-front-fill ride-icon"></i>
                            Bumper Cars
                          </label>
                        </div>
                        <div class="ride-option">
                          <input class="form-check-input" type="checkbox" id="ride6" value="Drop Tower" onchange="updateSelected()">
                          <label class="form-check-label" for="ride6">
                            <i class="bi bi-arrow-down-circle-fill ride-icon"></i>
                            Drop Tower
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Package Price -->
                <div class="col-md-6">
                  <div class="form-floating">
                    <input type="number" id="ridePrice" class="form-control form-control-custom" placeholder="0" min="0">
                    <label for="ridePrice">
                      <i class="bi bi-currency-dollar me-2"></i>Package Price (PKR)
                    </label>
                  </div>
                </div>

                <!-- Submit Button -->
                <div class="col-12">
                  <button type="button" onclick="addPackage()" class="btn btn-create-package">
                    <i class="bi bi-plus-lg me-2"></i>Create Package
                    <div class="btn-shine"></div>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Package List Section -->
      <div class="row">
        <div class="col-12">
          <div class="glass-card">
            <div class="card-header-custom">
              <h3 class="text-warning mb-0">
                <i class="bi bi-list-ul me-2"></i>Active Packages
              </h3>
            </div>

            <div class="packages-grid" id="packagesGrid">
              <!-- Packages will be displayed here -->
            </div>

            <!-- Fallback Table for Mobile -->
            <div class="table-responsive d-none" id="packageTableContainer">
              <table class="table table-dark table-hover">
                <thead>
                  <tr>
                    <th scope="col">
                      <i class="bi bi-tag-fill me-2"></i>Package Name
                    </th>
                    <th scope="col">
                      <i class="bi bi-collection-fill me-2"></i>Rides
                    </th>
                    <th scope="col">
                      <i class="bi bi-currency-dollar me-2"></i>Price
                    </th>
                    <th scope="col">
                      <i class="bi bi-gear-fill me-2"></i>Actions
                    </th>
                  </tr>
                </thead>
                <tbody id="packageTable"></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
  <script>
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');

      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      toggleBtn.classList.toggle('active');

      // Toggle icon
      toggleBtn.innerText = toggleBtn.innerText === '☰' ? '✖' : '☰';
    }
    function toggleReportsMenu() {
      const subMenu = document.getElementById('reportsSubMenu');
      subMenu.style.display = subMenu.style.display === 'none' ? 'block' : 'none';
    }
   
  </script>

 <script>
  function toggleDropdown() {
    document.getElementById("rideDropdown").classList.toggle("show");
  }

  function updateSelected() {
    const checkboxes = document.querySelectorAll("#rideDropdown input[type='checkbox']");
    const selectedDisplay = document.getElementById("selectedRidesDisplay");
    const selected = Array.from(checkboxes)
      .filter(cb => cb.checked)
      .map(cb => `<span class='selected-ride'>${cb.value}</span>`);
    selectedDisplay.innerHTML = selected.length > 0 ? selected.join(' ') : "Select Rides";
  }

  function filterRides() {
    const input = document.getElementById("rideSearch");
    const filter = input.value.toLowerCase();
    const rideList = document.getElementById("rideList");
    const items = rideList.getElementsByClassName("form-check");

    for (let i = 0; i < items.length; i++) {
      const label = items[i].getElementsByTagName("label")[0];
      const text = label.textContent || label.innerText;
      items[i].style.display = text.toLowerCase().includes(filter) ? "" : "none";
    }
  }

  function addPackage() {
    const dealName = document.getElementById("dealName").value.trim();
    const checkboxes = document.querySelectorAll("#rideDropdown input[type='checkbox']");
    const price = document.getElementById("ridePrice").value;

    const selectedRides = Array.from(checkboxes)
      .filter(cb => cb.checked)
      .map(cb => cb.value);

    if (!dealName || selectedRides.length === 0 || !price) {
      alert("Please enter deal name, select at least one ride, and enter the price.");
      return;
    }

    const table = document.getElementById("packageTable");
    const row = document.createElement("tr");
    row.innerHTML = `
      <td>${dealName}</td>
      <td>${selectedRides.join(', ')}</td>
      <td>PKR ${parseInt(price).toLocaleString()}</td>
    `;
    table.appendChild(row);

    // Reset
    document.getElementById("dealName").value = "";
    checkboxes.forEach(cb => cb.checked = false);
    document.getElementById("ridePrice").value = "";
    document.getElementById("rideDropdown").classList.remove("show");
    updateSelected();
  }

  document.addEventListener("click", function(event) {
    const dropdown = document.getElementById("rideDropdown");
    if (!dropdown.contains(event.target)) {
      dropdown.classList.remove("show");
    }
  });
</script>
</body>
</html>