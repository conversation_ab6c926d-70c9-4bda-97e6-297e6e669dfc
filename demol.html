<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Responsive Sidebar</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      overflow-x: hidden;
      background-color: #1c1c1c;
      color: #f8f9fa;
    }
    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }

    .sidebar a {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
    }

    .sidebar a:hover {
      background-color: #444;
      color: #ff9900;
    }

    .content {
      margin-left: 250px;
      padding: 20px;
      transition: all 0.3s;
    }

    .sidebar-collapsed {
      width: 0;
      overflow: hidden;
    }

    .content-expanded {
      margin-left: 0;
    }

    .toggle-btn {
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
    }

    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }

    .main-sections {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 30px;
    }

    .left-section {
      flex: 1;
      background-color: #2c2c2c;
      padding: 20px;
      border-radius: 10px;
      position: relative;
      overflow: hidden;
    }

    .watermark-line {
      position: relative;
      width: 100%;
      overflow: hidden;
      height: 40px;
      margin-bottom: 10px;
    }

    .watermark-text {
      position: absolute;
      white-space: nowrap;
      font-size: 24px;
      color: #ffcc66;
      animation: slideText 10s linear infinite;
      opacity: 0.3;
    }

    @keyframes slideText {
      0% { left: 100%; }
      100% { left: -100%; }
    }

    .right-section {
      width: 300px;
      background-color: #333;
      padding: 20px;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
        padding-top: 60px;
      }

      .content {
        margin-left: 0;
      }

      .main-sections {
        flex-direction: column;
      }

      .right-section {
        width: 100%;
      }
    }
  </style>
</head>
<body>

  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="#">Dashboard</a>
    <a href="#">Profile</a>
    <a href="#">Settings</a>
    <a href="#">Logout</a>
  </div>

  <div class="content" id="content">
    <div class="container-fluid">
      <h1 class="mt-4">Responsive Sidebar Layout</h1>
      <div class="main-sections">
        <div class="left-section" id="mainContent">
          <div class="watermark-line">
            <div class="watermark-text">FUN LAND • FUN LAND • FUN LAND • FUN LAND •</div>
          </div>
          <h4>Main Content</h4>
          <p>This is the main larger section where you can place your primary content.</p>
        </div>
        <div class="right-section">
          <button class="btn btn-warning text-dark" onclick="changeTab('Tab 1 Content')">Top Up</button>
          <button class="btn btn-warning text-dark" onclick="changeTab('Tab 2 Content')">Return</button>
          <button class="btn btn-warning text-dark" onclick="changeTab('Tab 3 Content')">Add Custumer</button>
        </div>
      </div>
    </div>
  </div>

  <div id="idleOverlay" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; z-index:2000; backdrop-filter:blur(8px); background:rgba(30,30,30,0.5);">
    <div id="funLandAnim" style="position:absolute; font-size:48px; color:#ffcc66; opacity:0.7; white-space:nowrap;">FUN LAND • FUN LAND • FUN LAND • FUN LAND •</div>
  </div>

  <script>
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');

      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      toggleBtn.classList.toggle('active');

      // Toggle icon
      toggleBtn.innerText = toggleBtn.innerText === '☰' ? '✖' : '☰';
    }

    // Dummy user data
    let user = {
      id: 'U12345',
      name: 'Ali Khan',
      balance: 500
    };

    function animateBalance(from, to, element) {
      const duration = 1000;
      const start = performance.now();

      function update(now) {
        const progress = Math.min((now - start) / duration, 1);
        const value = Math.floor(from + (to - from) * progress);
        element.innerText = value;
        if (progress < 1) {
          requestAnimationFrame(update);
        }
      }
      requestAnimationFrame(update);
    }

    function changeTab(contentText) {
      const mainContent = document.getElementById('mainContent');
      if (contentText === 'Tab 1 Content') {
        mainContent.innerHTML = `
          <div class="watermark-line">
            <div class="watermark-text">FUN LAND • FUN LAND • FUN LAND • FUN LAND •</div>
          </div>
          <h4>Top Up</h4>
          <div class="mb-3">
            <label>User ID: <b>${user.id}</b></label><br>
            <label>User Name: <b>${user.name}</b></label>
          </div>
          <div class="mb-3">
            <label for="topupAmount">Enter Top Up Amount:</label>
            <input type="number" id="topupAmount" class="form-control" min="1" style="max-width:150px;">
          </div>
          <div class="mb-3">
            <label>Current Balance: <b id="currentBalance">${user.balance}</b></label>
          </div>
          <button class="btn btn-success" onclick="doTopUp()">Add Cash</button>
        `;
      } else if (contentText === 'Tab 2 Content') {
        mainContent.innerHTML = `
          <div class="watermark-line">
            <div class="watermark-text">FUN LAND • FUN LAND • FUN LAND • FUN LAND •</div>
          </div>
          <h4>Return</h4>
          <div class="mb-3">
            <label>User ID: <b>${user.id}</b></label><br>
            <label>User Name: <b>${user.name}</b></label>
          </div>
          <div class="mb-3">
            <label for="returnAmount">Enter Return Amount:</label>
            <input type="number" id="returnAmount" class="form-control" min="1" style="max-width:150px;">
          </div>
          <div class="mb-3">
            <label>Current Balance: <b id="currentBalance">${user.balance}</b></label>
          </div>
          <button class="btn btn-danger" onclick="doReturn()">Return Cash</button>
        `;
      } else {
        mainContent.innerHTML = `
          <div class="watermark-line">
            <div class="watermark-text">FUN LAND • FUN LAND • FUN LAND • FUN LAND •</div>
          </div>
          <h4>${contentText}</h4>
          <p>This is the content for ${contentText.toLowerCase()}.</p>
        `;
      }
    }

    function doTopUp() {
      const amountInput = document.getElementById('topupAmount');
      const balanceElem = document.getElementById('currentBalance');
      let amount = parseInt(amountInput.value, 10);
      if (!amount || amount < 1) {
        alert('Enter a valid amount!');
        return;
      }
      const oldBalance = user.balance;
      user.balance += amount;
      animateBalance(oldBalance, user.balance, balanceElem);
      amountInput.value = '';
    }

    function doReturn() {
      const amountInput = document.getElementById('returnAmount');
      const balanceElem = document.getElementById('currentBalance');
      let amount = parseInt(amountInput.value, 10);
      if (!amount || amount < 1) {
        alert('Enter a valid amount!');
        return;
      }
      if (amount > user.balance) {
        alert('Return amount cannot be greater than current balance!');
        return;
      }
      const oldBalance = user.balance;
      user.balance -= amount;
      animateBalance(oldBalance, user.balance, balanceElem);
      amountInput.value = '';
    }

    let idleTimeout;
    let overlay = document.getElementById('idleOverlay');
    let animText = document.getElementById('funLandAnim');
    let animX = 0, animY = 0, dx = 2, dy = 1;

    function showIdleOverlay() {
      overlay.style.display = 'block';
      animX = 0; animY = 0;
      animateFunLand();
    }

    function hideIdleOverlay() {
      overlay.style.display = 'none';
      cancelAnimationFrame(animFrame);
    }

    function resetIdleTimer() {
      hideIdleOverlay();
      clearTimeout(idleTimeout);
      idleTimeout = setTimeout(showIdleOverlay, 5000); // 5 seconds idle
    }

    let animFrame;
    function animateFunLand() {
      animX += dx;
      animY += dy;
      // Bounce on edges
      if (animX + animText.offsetWidth > window.innerWidth || animX < 0) dx = -dx;
      if (animY + animText.offsetHeight > window.innerHeight || animY < 0) dy = -dy;
      animText.style.left = animX + 'px';
      animText.style.top = animY + 'px';
      animFrame = requestAnimationFrame(animateFunLand);
    }

    // Listen for user activity
    ['mousemove','keydown','mousedown','touchstart'].forEach(evt => {
      window.addEventListener(evt, resetIdleTimer);
    });

    resetIdleTimer();
  </script>

</body>
</html>
