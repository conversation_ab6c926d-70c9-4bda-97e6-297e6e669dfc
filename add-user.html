<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Settings - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      background-color: #1c1c1c;
      color: #f8f9fa;
      min-height: 100vh;
      overflow-x: hidden;
    }
    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }
    .sidebar.sidebar-collapsed {
      width: 0 !important;
      padding: 0 !important;
      overflow: hidden;
    }
    .sidebar a, .sidebar #reportsMenu {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
      transition: background 0.3s, color 0.3s, padding-left 0.3s;
    }
    .sidebar a:hover, .sidebar #reportsMenu:hover {
      background-color: #444;
      color: #ff9900;
      padding-left: 25px;
    }
    #reportsSubMenu {
      transition: max-height 0.4s, opacity 0.4s;
      overflow: hidden;
    }
    #reportsSubMenu[style*="display: block"] {
      opacity: 1;
      max-height: 200px;
    }
    #reportsSubMenu[style*="display: none"] {
      opacity: 0;
      max-height: 0;
    }
    #reportsSubMenu a {
      color: #ffcc66;
      padding: 10px 15px;
      display: block;
      text-decoration: none;
      font-size: 15px;
      transition: background 0.3s, color 0.3s;
    }
    #reportsSubMenu a:hover {
      background-color: #444;
      color: #ff9900;
    }
    .toggle-btn {
      position: absolute;
      top: 18px;
      left: 18px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
      font-size: 1.25rem; /* Chhota font size */
      padding: 4px 12px;
      border-radius: 8px;
      transition: background 0.3s;
    }
    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }
    .sidebar.sidebar-collapsed .toggle-btn {
      left: 18px; /* Sidebar collapse hone par bhi button sidebar ke andar rahe */
      position: absolute;
      top: 18px;
      display: block;
    }
    .content {
      margin-left: 250px;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: margin-left 0.3s;
    }
    .content.content-expanded {
      margin-left: 0 !important;
    }
    .glass-card {
      background: rgba(34, 34, 34, 0.7);
      border-radius: 22px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.22), 0 0 0 4px rgba(255,204,102,0.10);
      border: 1.5px solid rgba(255,204,102,0.18);
      backdrop-filter: blur(14px) brightness(1.08);
      padding: 32px 28px 28px 28px;
      animation: fadeIn 0.7s;
      position: relative;
      max-width: 900px;
      width: 100%;
      margin: 40px auto;
    }
    .card-icon-circle {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: linear-gradient(135deg, #ffcc66 60%, #fffbe6 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 18px;
      box-shadow: 0 2px 12px rgba(255,204,102,0.15);
    }
    .glass-card .form-label {
      font-weight: 500;
      color: #ffcc66;
      margin-bottom: 4px;
    }
    .glass-card input.form-control {
      background: rgba(255,255,255,0.07);
      border: 1px solid #ffcc66;
      color: #fff;
      border-radius: 8px;
    }
    .glass-card input.form-control:focus {
      border-color: #ff9900;
      background: rgba(255,255,255,0.13);
      color: #fff;
    }
    .glass-card input::placeholder {
      color: #fff !important;
      opacity:1;
    }
    .glass-card .btn {
      font-weight: 500;
      font-size: 1.08rem;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(255,204,102,0.10);
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .glass-card .btn:hover {
      transform: translateY(-2px) scale(1.03);
      box-shadow: 0 8px 24px rgba(255,204,102,0.18);
    }
    .btn.btn-warning.w-100 {
      width: auto !important;
      min-width: 120px;
      display: block;
      margin: 0 auto;
      padding: 6px 24px;
      font-size: 1rem;
    }
    .toggle-eye {
      position: absolute;
      top: 38px;
      right: 16px;
      cursor: pointer;
      color: #ffcc66;
      font-size: 1.2rem;
      z-index: 10;
      user-select: none;
    }
    .toggle-eye:hover {
      color: #ff9900;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px);}
      to { opacity: 1; transform: translateY(0);}
    }
    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
        padding-top: 60px;
      }
      .toggle-btn {
        left: 200px;
      }
      .sidebar.sidebar-collapsed ~ .toggle-btn {
        left: 10px;
      }
      .content {
        margin-left: 0 !important;
      }
      .glass-card {
        padding: 18px 8px 18px 8px;
      }
    }
  </style>
</head>
<body>
  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="./search.html">Search Customer</a>
    <a href="#" id="reportsMenu" onclick="toggleReportsMenu()">Reports ▼</a>
    <div id="reportsSubMenu" style="display:none; margin-left:20px;">
      <a href="./sale-report.html">Sale Report</a>
      <a href="./return-report.html">Return Report</a>
      <a href="./individual-report.html">Individual Report</a>
    </div>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./add-ride.html">Add Ride</a>
    <a href="./setting.html">Settings</a>
  </div>
  <div class="content" id="content">
    <div class="glass-card">
      <div class="card-icon-circle mx-auto mb-3">
        <i class="bi bi-person-gear fs-2 text-dark"></i>
      </div>
      <h3 class="mb-3 text-center text-warning">Add User</h3>
      <form>
        <div class="row">
          <div class="col-md-6 mb-3 d-flex flex-column">
            <label class="form-label">Name</label>
            <input type="text" class="form-control" id="userName" placeholder="Enter your name">
          </div>
          <div class="col-md-6 mb-3 d-flex flex-column">
            <label class="form-label">Phone Number</label>
            <input type="text" class="form-control" id="userPhone" placeholder="Enter your phone number">
          </div>
        </div>
        <div class="row">
          <div class="col-md-6 mb-3 d-flex flex-column">
            <label class="form-label">Email</label>
            <input type="email" class="form-control" id="userEmail" placeholder="Enter your email">
          </div>
          <div class="col-md-6 mb-3 position-relative d-flex flex-column">
            <label class="form-label">New Password</label>
            <input type="password" class="form-control" id="newPass" placeholder="New password">
            <span class="toggle-eye" onclick="togglePassword('newPass', this)">
              <i class="bi bi-eye-slash"></i>
            </span>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6 mb-3 position-relative d-flex flex-column">
            <label class="form-label">Confirm Password</label>
            <input type="password" class="form-control" id="confirmPass" placeholder="Confirm new password">
            <span class="toggle-eye" onclick="togglePassword('confirmPass', this)">
              <i class="bi bi-eye-slash"></i>
            </span>
          </div>
        </div>
        <button type="button" class="btn btn-warning w-100" onclick="saveSettings()">Save Settings</button>
      </form>

      
    </div>
     <div class="glass-card">
        <h4 class="mb-3">Assign User Role</h4>
        <div class="row">
          <div class="col-md-12">
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="userRole" id="roleAdmin" value="Admin">
              <label class="form-check-label" for="roleAdmin">Admin</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="userRole" id="roleManager" value="Manager">
              <label class="form-check-label" for="roleManager">Manager</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="userRole" id="roleEditor" value="Editor">
              <label class="form-check-label" for="roleEditor">Editor</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="userRole" id="roleViewer" value="Viewer">
              <label class="form-check-label" for="roleViewer">Viewer</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="userRole" id="roleEmployee" value="Employee">
              <label class="form-check-label" for="roleEmployee">Employee</label>
            </div>
          </div>
        </div>
      </div>
  </div>
  <script>
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');
      const openBtn = document.getElementById('openBtn');
      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      if (sidebar.classList.contains('sidebar-collapsed')) {
        sidebar.style.width = '0';
        toggleBtn.style.display = 'none';
        openBtn.style.display = 'block';
      } else {
        sidebar.style.width = '250px';
        toggleBtn.style.display = 'block';
        openBtn.style.display = 'none';
      }
    }
    function toggleReportsMenu() {
      const subMenu = document.getElementById('reportsSubMenu');
      subMenu.style.display = subMenu.style.display === 'none' ? 'block' : 'none';
    }
    function saveSettings() {
      const name = document.getElementById('userName').value;
      const phone = document.getElementById('userPhone').value;
      const email = document.getElementById('userEmail').value;
      const newPass = document.getElementById('newPass').value;
      const confirmPass = document.getElementById('confirmPass').value;
      if (!name || !phone || !email || !newPass || !confirmPass) {
        alert('Please fill all fields!');
        return;
      }
      if (newPass !== confirmPass) {
        alert('New password and confirm password do not match!');
        return;
      }
      alert('Settings saved (demo only)');
    }
    function togglePassword(inputId, el) {
      const input = document.getElementById(inputId);
      const icon = el.querySelector('i');
      if (input.type === "password") {
        input.type = "text";
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
      } else {
        input.type = "password";
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
      }
    }
    // Dummy changeTab for navigation (optional)
    function changeTab(tab) {
      // You can add navigation logic here if needed

      alert('Settings saved. Assigned Role: ' + role.value);
    }
  </script>
</body>
</html>