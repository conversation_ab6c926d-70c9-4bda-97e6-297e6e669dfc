<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Add Ride - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      overflow-x: hidden;
      background-color: #1c1c1c;
      color: #f8f9fa;
    }

    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }

    .sidebar a {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
    }

    .sidebar a:hover {
      background-color: #444;
      color: #ff9900;
      padding-left: 25px;
    }

    .content {
      margin-left: 250px;
      padding: 20px;
      transition: all 0.3s;
    }

    .sidebar-collapsed {
      width: 0;
      overflow: hidden;
    }

    .content-expanded {
      margin-left: 0;
    }

    .toggle-btn {
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
    }

    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }

    .glass-card {
      background: rgba(34, 34, 34, 0.7);
      border-radius: 22px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.22), 0 0 0 4px rgba(255, 204, 102, 0.10);
      border: 1.5px solid rgba(255, 204, 102, 0.18);
      backdrop-filter: blur(14px) brightness(1.08);
      padding: 32px 28px 28px 28px;
      animation: fadeIn 0.7s;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .stats-card {
      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      color: #fff;
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .table-dark {
      background-color: rgba(34, 34, 34, 0.9);
      border-radius: 10px;
    }

    .table-dark th {
      background-color: #28a745;
      color: #fff;
      border: none;
    }

    .table-dark td {
      border-color: #444;
      color: #f8f9fa;
    }

    .btn-success {
      background-color: #28a745;
      border-color: #28a745;
      color: #fff;
    }

    .btn-success:hover {
      background-color: #218838;
      border-color: #1e7e34;
      color: white;
    }

    .form-control {
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid #28a745;
      color: #fff;
      border-radius: 8px;
    }

    .form-control:focus {
      border-color: #20c997;
      background-color: rgba(255, 255, 255, 0.15);
      color: #fff;
      box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .form-control::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }

    .form-label {
      color: #28a745;
      font-weight: 500;
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
      }
      .sidebar.sidebar-collapsed {
        width: 0 !important;
      }
      .content {
        margin-left: 0 !important;
      }
      .glass-card {
        padding: 20px 15px;
      }
      .stats-card {
        margin-bottom: 15px;
      }
    }

    @media (max-width: 576px) {
      .glass-card {
        padding: 15px 10px;
      }
      .table-responsive {
        font-size: 0.9rem;
      }
    }
  </style>
</head>

<body>

  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="#" onclick="showSearchCustomer()">Search Customer</a>
    <a href="#" onclick="showReports()">Reports</a>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./add-ride.html">Add Ride</a>
    <a href="./setting.html">Settings</a>
  </div>

  <div class="content" id="content">
    <div class="container-fluid">
      
      <!-- Add Ride Section -->
      <div id="addRideSection">
        <div class="row">
          <!-- Add Ride Form -->
          <div class="col-lg-6 mb-4">
            <div class="glass-card">
              <h2 class="text-success mb-4">
                <i class="bi bi-plus-circle"></i> Add New Ride
              </h2>
              
              <form id="addRideForm">
                <div class="mb-3">
                  <label for="rideId" class="form-label">Ride IP</label>
                  <input type="text" id="rideId" class="form-control" placeholder="Enter ride IP (e.g., ***********)" required>
                </div>

                <div class="mb-3">
                  <label for="rideName" class="form-label">Ride Name</label>
                  <input type="text" id="rideName" class="form-control" placeholder="Enter ride name" required>
                </div>

                <div class="mb-3">
                  <label for="ridePrice" class="form-label">Ride Price (PKR)</label>
                  <input type="number" id="ridePrice" class="form-control" placeholder="Enter price" min="1" required>
                </div>

                <div class="mb-3">
                  <label for="rideDuration" class="form-label">Ride Duration (minutes)</label>
                  <input type="number" id="rideDuration" class="form-control" placeholder="Enter duration" min="1" required>
                </div>

                <button type="submit" class="btn btn-success w-100 mb-3">
                  <i class="bi bi-check-circle"></i> Add Ride
                </button>

                <button type="button" class="btn btn-info w-100" onclick="showViewRides()">
                  <i class="bi bi-eye"></i> View Rides
                </button>
              </form>
            </div>
          </div>

          <!-- Stats Cards -->
          <div class="col-lg-6">
            <div class="row">
              <div class="col-md-6 mb-3">
                <div class="stats-card text-center">
                  <h4 id="totalRidesCount">0</h4>
                  <p class="mb-0">Total Rides</p>
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <div class="stats-card text-center">
                  <h4 id="avgRidePrice">PKR 0</h4>
                  <p class="mb-0">Average Price</p>
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <div class="stats-card text-center">
                  <h4 id="maxPrice">PKR 0</h4>
                  <p class="mb-0">Highest Price</p>
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <div class="stats-card text-center">
                  <h4 id="minPrice">PKR 0</h4>
                  <p class="mb-0">Lowest Price</p>
                </div>
              </div>
            </div>

            <!-- Recent Rides -->
            <div class="glass-card">
              <h5 class="text-success mb-3">
                <i class="bi bi-clock-history"></i> Recent Rides
              </h5>
              <div id="recentRidesList">
                <p class="text-muted">No rides added yet</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- View Rides Section -->
      <div id="viewRidesSection" style="display:none;">
        <div class="glass-card">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-success mb-0">
              <i class="bi bi-list-ul"></i> All Rides
            </h2>
            <button class="btn btn-secondary" onclick="showAddRide()">
              <i class="bi bi-arrow-left"></i> Back to Add
            </button>
          </div>

          <!-- Search and Filter -->
          <div class="row mb-4">
            <div class="col-md-6">
              <input type="text" id="searchRide" class="form-control" placeholder="Search rides..." onkeyup="searchRides()">
            </div>
            <div class="col-md-3">
              <select id="sortBy" class="form-control" onchange="sortRides()">
                <option value="name">Sort by Name</option>
                <option value="price">Sort by Price</option>
                <option value="duration">Sort by Duration</option>
              </select>
            </div>
            <div class="col-md-3">
              <button class="btn btn-warning w-100" onclick="exportRides()">
                <i class="bi bi-download"></i> Export
              </button>
            </div>
          </div>

          <!-- Rides Table -->
          <div class="table-responsive">
            <table class="table table-dark table-striped">
              <thead>
                <tr>
                  <th>Ride ID</th>
                  <th>Ride Name</th>
                  <th>Price</th>
                  <th>Duration</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="ridesTableBody">
                <!-- Rides will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

    </div>
  </div>

  <script>
    // Rides data storage
    let ridesData = [
      {
        id: 'R001',
        name: 'Roller Coaster',
        price: 150,
        duration: 5
      },
      {
        id: 'R002',
        name: 'Ferris Wheel',
        price: 100,
        duration: 10
      },
      {
        id: 'R003',
        name: 'Bumper Cars',
        price: 80,
        duration: 8
      }
    ];

    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');

      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      toggleBtn.classList.toggle('active');

      toggleBtn.innerText = toggleBtn.innerText === '☰' ? '✖' : '☰';
    }

    function showViewRides() {
      document.getElementById('addRideSection').style.display = 'none';
      document.getElementById('viewRidesSection').style.display = 'block';
      populateRidesTable();
    }

    function showAddRide() {
      document.getElementById('viewRidesSection').style.display = 'none';
      document.getElementById('addRideSection').style.display = 'block';
      updateStats();
      updateRecentRides();
    }

    // Add ride form submission
    document.getElementById('addRideForm').addEventListener('submit', function(e) {
      e.preventDefault();

      const rideId = document.getElementById('rideId').value.trim();
      const rideName = document.getElementById('rideName').value.trim();
      const ridePrice = parseInt(document.getElementById('ridePrice').value);
      const rideDuration = parseInt(document.getElementById('rideDuration').value);

      // Validation
      if (!rideId || !rideName || !ridePrice || !rideDuration) {
        alert('Please fill all fields!');
        return;
      }

      // Check if ride ID already exists
      if (ridesData.find(ride => ride.id === rideId)) {
        alert('Ride ID already exists! Please use a different ID.');
        return;
      }

      // Add new ride
      const newRide = {
        id: rideId,
        name: rideName,
        price: ridePrice,
        duration: rideDuration
      };

      ridesData.push(newRide);

      // Clear form
      document.getElementById('addRideForm').reset();

      // Update stats and recent rides
      updateStats();
      updateRecentRides();

      alert(`Ride added successfully!\nID: ${rideId}\nName: ${rideName}\nPrice: PKR ${ridePrice}\nDuration: ${rideDuration} minutes`);
    });

    function updateStats() {
      const totalRides = ridesData.length;
      const totalPrice = ridesData.reduce((sum, ride) => sum + ride.price, 0);
      const avgPrice = totalRides > 0 ? Math.round(totalPrice / totalRides) : 0;
      const maxPrice = totalRides > 0 ? Math.max(...ridesData.map(ride => ride.price)) : 0;
      const minPrice = totalRides > 0 ? Math.min(...ridesData.map(ride => ride.price)) : 0;

      document.getElementById('totalRidesCount').textContent = totalRides;
      document.getElementById('avgRidePrice').textContent = `PKR ${avgPrice}`;
      document.getElementById('maxPrice').textContent = `PKR ${maxPrice}`;
      document.getElementById('minPrice').textContent = `PKR ${minPrice}`;
    }

    function updateRecentRides() {
      const recentList = document.getElementById('recentRidesList');

      if (ridesData.length === 0) {
        recentList.innerHTML = '<p class="text-muted">No rides added yet</p>';
        return;
      }

      const recentRides = ridesData.slice(-3).reverse(); // Last 3 rides
      let html = '';

      recentRides.forEach(ride => {
        html += `
          <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-dark rounded">
            <div>
              <strong>${ride.name}</strong><br>
              <small class="text-muted">${ride.id}</small>
            </div>
            <div class="text-end">
              <div class="text-success">PKR ${ride.price}</div>
              <small class="text-muted">${ride.duration} min</small>
            </div>
          </div>
        `;
      });

      recentList.innerHTML = html;
    }

    function populateRidesTable() {
      const tbody = document.getElementById('ridesTableBody');

      if (ridesData.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No rides available</td></tr>';
        return;
      }

      let html = '';
      ridesData.forEach((ride, index) => {
        html += `
          <tr>
            <td>${ride.id}</td>
            <td>${ride.name}</td>
            <td>PKR ${ride.price}</td>
            <td>${ride.duration} min</td>
            <td>
              <button class="btn btn-sm btn-warning me-1" onclick="editRide(${index})">
                <i class="bi bi-pencil"></i>
              </button>
              <button class="btn btn-sm btn-danger" onclick="deleteRide(${index})">
                <i class="bi bi-trash"></i>
              </button>
            </td>
          </tr>
        `;
      });

      tbody.innerHTML = html;
    }

    function editRide(index) {
      const ride = ridesData[index];
      const newName = prompt('Enter new ride name:', ride.name);
      const newPrice = prompt('Enter new price:', ride.price);
      const newDuration = prompt('Enter new duration (minutes):', ride.duration);

      if (newName && newPrice && newDuration) {
        ridesData[index] = {
          ...ride,
          name: newName,
          price: parseInt(newPrice),
          duration: parseInt(newDuration)
        };

        populateRidesTable();
        updateStats();
        updateRecentRides();
        alert('Ride updated successfully!');
      }
    }

    function deleteRide(index) {
      const ride = ridesData[index];
      if (confirm(`Are you sure you want to delete "${ride.name}"?`)) {
        ridesData.splice(index, 1);
        populateRidesTable();
        updateStats();
        updateRecentRides();
        alert('Ride deleted successfully!');
      }
    }

    function searchRides() {
      const searchTerm = document.getElementById('searchRide').value.toLowerCase();
      const filteredRides = ridesData.filter(ride =>
        ride.name.toLowerCase().includes(searchTerm) ||
        ride.id.toLowerCase().includes(searchTerm)
      );

      displayFilteredRides(filteredRides);
    }

    function sortRides() {
      const sortBy = document.getElementById('sortBy').value;
      let sortedRides = [...ridesData];

      switch(sortBy) {
        case 'name':
          sortedRides.sort((a, b) => a.name.localeCompare(b.name));
          break;
        case 'price':
          sortedRides.sort((a, b) => b.price - a.price);
          break;
        case 'duration':
          sortedRides.sort((a, b) => b.duration - a.duration);
          break;
      }

      displayFilteredRides(sortedRides);
    }

    function displayFilteredRides(rides) {
      const tbody = document.getElementById('ridesTableBody');

      if (rides.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No rides found</td></tr>';
        return;
      }

      let html = '';
      rides.forEach((ride, index) => {
        const originalIndex = ridesData.findIndex(r => r.id === ride.id);
        html += `
          <tr>
            <td>${ride.id}</td>
            <td>${ride.name}</td>
            <td>PKR ${ride.price}</td>
            <td>${ride.duration} min</td>
            <td>
              <button class="btn btn-sm btn-warning me-1" onclick="editRide(${originalIndex})">
                <i class="bi bi-pencil"></i>
              </button>
              <button class="btn btn-sm btn-danger" onclick="deleteRide(${originalIndex})">
                <i class="bi bi-trash"></i>
              </button>
            </td>
          </tr>
        `;
      });

      tbody.innerHTML = html;
    }

    function exportRides() {
      let csvContent = "Ride ID,Ride Name,Price (PKR),Duration (min)\n";
      ridesData.forEach(ride => {
        csvContent += `${ride.id},${ride.name},${ride.price},${ride.duration}\n`;
      });

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'rides_data.csv';
      a.click();
      window.URL.revokeObjectURL(url);
    }

    // Initialize page
    window.onload = function() {
      updateStats();
      updateRecentRides();
    };
  </script>

</body>
</html>
