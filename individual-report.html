<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Individual Report - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      overflow-x: hidden;
      background-color: #1c1c1c;
      color: #f8f9fa;
    }

    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }

    .sidebar a {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
    }

    .sidebar a:hover {
      background-color: #444;
      color: #ff9900;
      padding-left: 25px;
    }

    .content {
      margin-left: 250px;
      padding: 20px;
      transition: all 0.3s;
    }

    .sidebar-collapsed {
      width: 0;
      overflow: hidden;
    }

    .content-expanded {
      margin-left: 0;
    }

    .toggle-btn {
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
    }

    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }

    .glass-card {
      background: rgba(34, 34, 34, 0.7);
      border-radius: 22px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.22), 0 0 0 4px rgba(255, 204, 102, 0.10);
      border: 1.5px solid rgba(255, 204, 102, 0.18);
      backdrop-filter: blur(14px) brightness(1.08);
      padding: 32px 28px 28px 28px;
      animation: fadeIn 0.7s;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .stats-card {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: #fff;
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
    }

    .table-dark {
      background-color: rgba(34, 34, 34, 0.9);
      border-radius: 10px;
    }

    .table-dark th {
      background-color: #17a2b8;
      color: #fff;
      border: none;
    }

    .table-dark td {
      border-color: #444;
      color: #f8f9fa;
    }

    .btn-info {
      background-color: #17a2b8;
      border-color: #17a2b8;
      color: #fff;
    }

    .btn-info:hover {
      background-color: #138496;
      border-color: #117a8b;
      color: white;
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
      }
      .sidebar.sidebar-collapsed {
        width: 0 !important;
      }
      .content {
        margin-left: 0 !important;
      }
    }
  </style>
</head>

<body>

  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="#" onclick="showSearchCustomer()">Search Customer</a>
    <a href="#" onclick="showReports()">Reports</a>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./setting.html">Settings</a>
    <a href="#" onclick="showRideBooking()">Book Ride</a>
  </div>

  <div class="content" id="content">
    <div class="container-fluid">
      
      <!-- Individual Report Section -->
      <div id="individualReportSection">
        <div class="glass-card">
          <h2 class="text-info mb-4">
            <i class="bi bi-person-lines-fill"></i> Individual Ride Report
          </h2>
          
          <!-- Stats Cards -->
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="totalRides">0</h4>
                <p class="mb-0">Total Rides</p>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="totalRideRevenue">PKR 0</h4>
                <p class="mb-0">Total Revenue</p>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="todayRides">0</h4>
                <p class="mb-0">Today's Rides</p>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="avgRidePrice">PKR 0</h4>
                <p class="mb-0">Average Price</p>
              </div>
            </div>
          </div>

          <!-- Filter Section -->
          <div class="row mb-4">
            <div class="col-md-3">
              <label class="form-label text-info">From Date</label>
              <input type="date" id="fromDate" class="form-control bg-dark text-light border-info">
            </div>
            <div class="col-md-3">
              <label class="form-label text-info">To Date</label>
              <input type="date" id="toDate" class="form-control bg-dark text-light border-info">
            </div>
            <div class="col-md-3">
              <label class="form-label text-info">Customer ID</label>
              <input type="text" id="customerFilter" class="form-control bg-dark text-light border-info" placeholder="Enter Customer ID">
            </div>
            <div class="col-md-3 d-flex align-items-end">
              <button class="btn btn-info w-100" onclick="filterRides()">
                <i class="bi bi-funnel"></i> Filter
              </button>
            </div>
          </div>

          <!-- Rides Table -->
          <div class="table-responsive">
            <table class="table table-dark table-striped">
              <thead>
                <tr>
                  <th>Ride ID</th>
                  <th>Ride Name</th>
                  <th>Date & Time</th>
                  <th>Customer ID</th>
                  <th>Customer Name</th>
                  <th>Ride Price</th>
                </tr>
              </thead>
              <tbody id="ridesTableBody">
                <!-- Ride data will be populated here -->
              </tbody>
            </table>
          </div>

          <!-- Export Buttons -->
          <div class="row mt-4">
            <div class="col-md-6">
              <button class="btn btn-success w-100" onclick="exportToExcel()">
                <i class="bi bi-file-earmark-excel"></i> Export to Excel
              </button>
            </div>
            <div class="col-md-6">
              <button class="btn btn-warning w-100" onclick="printReport()">
                <i class="bi bi-printer"></i> Print Report
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Ride Booking Section -->
      <div id="rideBookingSection" style="display:none;">
        <div class="glass-card mx-auto" style="max-width:500px;">
          <h2 class="text-info mb-4 text-center">
            <i class="bi bi-ticket-perforated"></i> Book Ride
          </h2>
          
          <div class="mb-3" id="rideScanSection">
            <button class="btn btn-primary w-100" onclick="scanRideCard()">
              <i class="bi bi-upc-scan"></i> Scan Customer Card
            </button>
          </div>

          <div id="rideForm" style="display:none;">
            <div class="row mb-3">
              <div class="col">
                <label class="form-label text-info">Customer ID</label>
                <div class="fw-bold text-warning" id="rideCustomerId"></div>
              </div>
              <div class="col">
                <label class="form-label text-info">Customer Name</label>
                <div class="fw-bold text-warning" id="rideCustomerName"></div>
              </div>
            </div>
            
            <div class="mb-3">
              <label class="form-label text-info">Current Balance</label>
              <div class="fw-bold fs-4 text-success" id="rideCurrentBalance"></div>
            </div>

            <div class="mb-3">
              <label for="rideName" class="form-label text-info">Select Ride</label>
              <select id="rideName" class="form-control bg-dark text-light border-info">
                <option value="">Select a ride</option>
                <option value="Roller Coaster" data-price="150">Roller Coaster - PKR 150</option>
                <option value="Ferris Wheel" data-price="100">Ferris Wheel - PKR 100</option>
                <option value="Bumper Cars" data-price="80">Bumper Cars - PKR 80</option>
                <option value="Carousel" data-price="60">Carousel - PKR 60</option>
                <option value="Water Slide" data-price="120">Water Slide - PKR 120</option>
                <option value="Haunted House" data-price="90">Haunted House - PKR 90</option>
              </select>
            </div>

            <div class="mb-3">
              <label class="form-label text-info">Ride Price</label>
              <div class="fw-bold fs-4 text-warning" id="selectedRidePrice">PKR 0</div>
            </div>

            <button class="btn btn-info w-100 mt-3" onclick="bookRide()">
              <i class="bi bi-check-circle"></i> Book Ride
            </button>
          </div>
        </div>
      </div>

    </div>
  </div>

  <script>
    // Sample ride data
    let rideData = [
      {
        rideId: 'RIDE001',
        rideName: 'Roller Coaster',
        date: '2024-01-15 14:30:00',
        customerId: 'CUST001',
        customerName: 'Ali Khan',
        price: 150
      },
      {
        rideId: 'RIDE002',
        rideName: 'Ferris Wheel',
        date: '2024-01-15 15:45:00',
        customerId: 'CUST002',
        customerName: 'Sara Ahmed',
        price: 100
      },
      {
        rideId: 'RIDE003',
        rideName: 'Bumper Cars',
        date: '2024-01-15 16:20:00',
        customerId: 'CUST003',
        customerName: 'Hassan Ali',
        price: 80
      },
      {
        rideId: 'RIDE004',
        rideName: 'Water Slide',
        date: '2024-01-15 17:10:00',
        customerId: 'CUST001',
        customerName: 'Ali Khan',
        price: 120
      }
    ];

    let rideCustomer = null;

    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');

      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      toggleBtn.classList.toggle('active');

      toggleBtn.innerText = toggleBtn.innerText === '☰' ? '✖' : '☰';
    }

    function showRideBooking() {
      document.getElementById('individualReportSection').style.display = 'none';
      document.getElementById('rideBookingSection').style.display = 'block';
    }

    function showReports() {
      document.getElementById('rideBookingSection').style.display = 'none';
      document.getElementById('individualReportSection').style.display = 'block';
      loadRideReport();
    }

    function loadRideReport() {
      updateStats();
      populateRidesTable(rideData);
    }

    function updateStats() {
      const totalRides = rideData.length;
      const totalRevenue = rideData.reduce((sum, ride) => sum + ride.price, 0);
      const todaysRides = rideData.filter(ride =>
        new Date(ride.date).toDateString() === new Date().toDateString()
      ).length;

      document.getElementById('totalRides').textContent = totalRides;
      document.getElementById('totalRideRevenue').textContent = `PKR ${totalRevenue}`;
      document.getElementById('todayRides').textContent = todaysRides;
      document.getElementById('avgRidePrice').textContent = `PKR ${Math.round(totalRevenue / totalRides) || 0}`;
    }

    function populateRidesTable(data) {
      const tbody = document.getElementById('ridesTableBody');
      tbody.innerHTML = '';

      data.forEach(ride => {
        const row = `
          <tr>
            <td>${ride.rideId}</td>
            <td>${ride.rideName}</td>
            <td>${new Date(ride.date).toLocaleString()}</td>
            <td>${ride.customerId}</td>
            <td>${ride.customerName}</td>
            <td>PKR ${ride.price}</td>
          </tr>
        `;
        tbody.innerHTML += row;
      });
    }

    function scanRideCard() {
      rideCustomer = {
        id: 'CUST' + Math.floor(Math.random() * 1000),
        name: 'Customer ' + Math.floor(Math.random() * 100),
        balance: Math.floor(Math.random() * 2000) + 500
      };

      document.getElementById('rideScanSection').style.display = 'none';
      document.getElementById('rideForm').style.display = 'block';
      document.getElementById('rideCustomerId').textContent = rideCustomer.id;
      document.getElementById('rideCustomerName').textContent = rideCustomer.name;
      document.getElementById('rideCurrentBalance').textContent = `PKR ${rideCustomer.balance}`;
    }

    // Update ride price when ride is selected
    document.addEventListener('DOMContentLoaded', function() {
      const rideSelect = document.getElementById('rideName');
      const priceDisplay = document.getElementById('selectedRidePrice');

      if (rideSelect) {
        rideSelect.addEventListener('change', function() {
          const selectedOption = this.options[this.selectedIndex];
          const price = selectedOption.getAttribute('data-price') || 0;
          priceDisplay.textContent = `PKR ${price}`;
        });
      }
    });

    function bookRide() {
      const rideSelect = document.getElementById('rideName');
      const selectedRide = rideSelect.value;
      const selectedOption = rideSelect.options[rideSelect.selectedIndex];
      const price = parseInt(selectedOption.getAttribute('data-price')) || 0;

      if (!selectedRide) {
        alert('Please select a ride!');
        return;
      }

      if (price > rideCustomer.balance) {
        alert('Insufficient balance!');
        return;
      }

      // Add new ride booking to data
      const newRide = {
        rideId: 'RIDE' + String(rideData.length + 1).padStart(3, '0'),
        rideName: selectedRide,
        date: new Date().toISOString(),
        customerId: rideCustomer.id,
        customerName: rideCustomer.name,
        price: price
      };

      rideData.push(newRide);

      // Update customer balance
      rideCustomer.balance -= price;
      document.getElementById('rideCurrentBalance').textContent = `PKR ${rideCustomer.balance}`;

      // Clear form
      document.getElementById('rideName').value = '';
      document.getElementById('selectedRidePrice').textContent = 'PKR 0';

      alert(`Ride booked successfully!\nRide ID: ${newRide.rideId}\nRide: ${selectedRide}\nPrice: PKR ${price}`);
    }

    function filterRides() {
      // Filter functionality can be implemented here
      alert('Filter functionality will be implemented based on requirements');
    }

    function exportToExcel() {
      alert('Excel export functionality will be implemented');
    }

    function printReport() {
      window.print();
    }

    // Load ride report by default
    window.onload = function() {
      loadRideReport();
    };
  </script>

</body>
</html>
