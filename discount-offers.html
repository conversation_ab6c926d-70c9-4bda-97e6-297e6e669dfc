<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Discount Offers - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    body {
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      color: #fff;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      min-height: 100vh;
    }

    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      width: 250px;
      height: 100vh;
      background: rgba(34, 34, 34, 0.95);
      backdrop-filter: blur(10px);
      border-right: 2px solid #ffcc66;
      padding: 20px 0;
      transition: all 0.3s;
      z-index: 1000;
    }

    .sidebar a {
      display: block;
      color: #fff;
      text-decoration: none;
      padding: 15px 25px;
      transition: all 0.3s;
      border-left: 4px solid transparent;
    }

    .sidebar a:hover, .sidebar a.active {
      background: rgba(255, 204, 102, 0.1);
      border-left-color: #ffcc66;
      color: #ffcc66;
    }

    .sidebar.sidebar-collapsed {
      width: 0;
      overflow: hidden;
    }

    .toggle-btn {
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
    }

    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }

    .content {
      margin-left: 250px;
      min-height: 100vh;
      padding: 20px;
      transition: margin-left 0.3s;
    }

    .content.content-expanded {
      margin-left: 0;
    }

    /* Page Header */
    .page-header {
      text-align: center;
      margin-bottom: 40px;
      padding: 30px 0;
    }

    .page-header h1 {
      color: #ffcc66;
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 10px;
    }

    .page-header p {
      color: #aaa;
      font-size: 1.1rem;
    }

    /* Main Container */
    .discount-container {
      max-width: 800px;
      margin: 0 auto;
    }

    /* Card Scanner Section */
    .scanner-card {
      background: #2a2a2a;
      border-radius: 15px;
      border: 2px solid #ffcc66;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      overflow: hidden;
      margin-bottom: 30px;
    }

    .card-header {
      background: linear-gradient(135deg, #ffcc66, #ff9900);
      color: #000;
      padding: 20px 25px;
      font-weight: 700;
    }

    .card-header h3 {
      margin: 0;
      font-size: 1.3rem;
    }

    .card-body {
      padding: 30px 25px;
    }

    /* Input Groups */
    .input-group-custom {
      margin-bottom: 25px;
    }

    .input-group-custom label {
      display: block;
      color: #ffcc66;
      font-weight: 600;
      margin-bottom: 8px;
      font-size: 0.95rem;
    }

    .form-input {
      width: 100%;
      padding: 15px;
      background: #1a1a1a;
      border: 2px solid #444;
      border-radius: 8px;
      color: #fff;
      font-size: 16px;
      transition: all 0.3s ease;
    }

    .form-input:focus {
      outline: none;
      border-color: #ffcc66;
      background: #222;
    }

    /* Card Scanner Input */
    .card-scanner {
      position: relative;
    }

    .scanner-input {
      padding-left: 50px;
      font-family: 'Courier New', monospace;
      font-size: 18px;
      letter-spacing: 2px;
    }

    .scanner-icon {
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #ffcc66;
      font-size: 1.5rem;
    }

    /* Price Input */
    .price-input {
      padding-left: 50px;
      font-size: 18px;
      font-weight: 600;
    }

    .price-icon {
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #ffcc66;
      font-size: 1.2rem;
    }

    /* Calculate Button */
    .calculate-btn {
      width: 100%;
      background: linear-gradient(135deg, #ffcc66, #ff9900);
      border: none;
      color: #000;
      padding: 15px;
      border-radius: 8px;
      font-size: 1.1rem;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .calculate-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255,204,102,0.4);
    }

    .calculate-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    /* Results Section */
    .results-card {
      background: #2a2a2a;
      border-radius: 15px;
      border: 2px solid #28a745;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      overflow: hidden;
      display: none;
    }

    .results-header {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: #fff;
      padding: 20px 25px;
      font-weight: 700;
    }

    .results-body {
      padding: 30px 25px;
    }

    .result-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid #444;
    }

    .result-item:last-child {
      border-bottom: none;
      font-size: 1.2rem;
      font-weight: 700;
      color: #28a745;
    }

    .result-label {
      color: #aaa;
      font-weight: 500;
    }

    .result-value {
      color: #fff;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .discount-display {
      display: flex;
      gap: 15px;
      align-items: center;
    }

    .discount-percentage {
      background: #ff6b6b;
      color: #fff;
      padding: 8px 15px;
      border-radius: 20px;
      font-weight: 700;
      font-size: 1.1rem;
    }

    .discount-amount {
      background: #28a745;
      color: #fff;
      padding: 8px 15px;
      border-radius: 20px;
      font-weight: 700;
      font-size: 1.1rem;
    }

    /* Button Group */
    .button-group {
      display: flex;
      gap: 10px;
    }

    .half-btn {
      flex: 1;
      padding: 12px;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
      border: none;
    }

    .save-btn {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: #fff;
    }

    .save-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(40,167,69,0.4);
    }

    .save-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    /* Offers Container */
    .offers-container {
      background: #2a2a2a;
      border-radius: 15px;
      border: 2px solid #ffcc66;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      overflow: hidden;
      height: fit-content;
    }

    .offers-header {
      background: linear-gradient(135deg, #ffcc66, #ff9900);
      color: #000;
      padding: 20px 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .offers-header h3 {
      margin: 0;
      font-size: 1.3rem;
      font-weight: 700;
    }

    .offers-count {
      background: rgba(0,0,0,0.2);
      padding: 5px 12px;
      border-radius: 15px;
      font-size: 0.9rem;
      font-weight: 600;
    }

    .offers-search {
      padding: 15px 20px 0 20px;
      border-bottom: 1px solid #444;
    }

    .offers-search .search-box {
      margin-bottom: 0;
    }

    .offers-search .search-box input {
      background: #1a1a1a;
      border: 2px solid #444;
      color: #fff;
      padding: 12px 12px 12px 40px;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .offers-search .search-box input:focus {
      border-color: #ffcc66;
      background: #222;
    }

    .offers-search .search-box i {
      color: #888;
      left: 15px;
    }

    .offers-list {
      padding: 20px;
      max-height: 600px;
      overflow-y: auto;
    }

    /* Offer Item */
    .offer-item {
      background: #1a1a1a;
      border: 2px solid #444;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 15px;
      transition: all 0.3s ease;
    }

    .offer-item:hover {
      border-color: #ffcc66;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(255,204,102,0.2);
    }

    .offer-header-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .offer-name {
      color: #ffcc66;
      font-size: 1.3rem;
      font-weight: 700;
      margin: 0;
    }

    .offer-status {
      background: #28a745;
      color: #fff;
      padding: 5px 12px;
      border-radius: 15px;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .offer-details {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 15px;
      margin-bottom: 15px;
    }

    .offer-detail {
      text-align: center;
    }

    .offer-detail-label {
      color: #aaa;
      font-size: 0.8rem;
      margin-bottom: 5px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .offer-detail-value {
      color: #fff;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .offer-actions {
      display: flex;
      gap: 10px;
    }

    .btn-apply, .btn-edit-offer, .btn-delete-offer {
      flex: 1;
      padding: 10px 15px;
      border: none;
      border-radius: 6px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.9rem;
    }

    .btn-apply {
      background: #ffcc66;
      color: #000;
    }

    .btn-apply:hover {
      background: #ff9900;
      transform: translateY(-1px);
    }

    .btn-edit-offer {
      background: #17a2b8;
      color: #fff;
    }

    .btn-edit-offer:hover {
      background: #138496;
      transform: translateY(-1px);
    }

    .btn-delete-offer {
      background: #dc3545;
      color: #fff;
    }

    .btn-delete-offer:hover {
      background: #c82333;
      transform: translateY(-1px);
    }

    /* Empty State */
    .empty-offers {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-offers i {
      font-size: 4rem;
      margin-bottom: 20px;
      display: block;
      color: #555;
    }

    .empty-offers h4 {
      color: #888;
      margin-bottom: 10px;
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
        padding-top: 60px;
      }
      .content {
        margin-left: 0 !important;
      }
      .discount-container {
        padding: 0 15px;
      }
      .page-header h1 {
        font-size: 2rem;
      }
      .discount-display {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
      }
      .discount-percentage, .discount-amount {
        font-size: 1rem;
        padding: 6px 12px;
      }
    }
  </style>
</head>

<body>
  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="./search.html">Search Customer</a>
    <a href="#" id="reportsMenu" onclick="toggleReportsMenu()">Reports ▼</a>
    <div id="reportsSubMenu" style="display:none; margin-left:20px;">
      <a href="./sale-report.html">Sale Report</a>
      <a href="./return-report.html">Return Report</a>
      <a href="./individual-report.html">Individual Report</a>
    </div>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./add-ride.html">Add Ride</a>
    <a href="./discount-offers.html" class="active">Discount Offers</a>
    <a href="./setting.html">Settings</a>
  </div>

  <div class="content" id="content">
    <div class="discount-container">

      <!-- Page Header -->
      <div class="page-header">
        <h1><i class="bi bi-percent me-3"></i>Discount Offers Management</h1>
        <p>Create, manage and apply discount offers</p>
      </div>

      <div class="row">
        <!-- Create Discount Section -->
        <div class="col-lg-5">
          <div class="scanner-card">
            <div class="card-header">
              <h3><i class="bi bi-plus-circle me-2"></i>Create Discount Offer</h3>
            </div>

            <div class="card-body">
              <!-- Offer Name -->
              <div class="input-group-custom">
                <label>Offer Name</label>
                <div class="card-scanner">
                  <i class="bi bi-tag price-icon"></i>
                  <input type="text" id="offerName" class="form-input price-input" placeholder="Enter offer name">
                </div>
              </div>

              <!-- Price Input -->
              <div class="input-group-custom">
                <label>Enter Amount (PKR)</label>
                <div class="card-scanner">
                  <i class="bi bi-currency-dollar price-icon"></i>
                  <input type="number" id="priceAmount" class="form-input price-input" placeholder="0" min="0" step="0.01">
                </div>
              </div>

              <!-- Discount Percentage Input -->
              <div class="input-group-custom">
                <label>Discount Percentage (%)</label>
                <div class="card-scanner">
                  <i class="bi bi-percent price-icon"></i>
                  <input type="number" id="discountPercent" class="form-input price-input" placeholder="0" min="0" max="100" step="0.1">
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="button-group">
                <button type="button" onclick="calculateDiscount()" class="calculate-btn half-btn" id="calculateBtn">
                  <i class="bi bi-calculator me-2"></i>Preview
                </button>
                <button type="button" onclick="saveOffer()" class="save-btn half-btn" id="saveBtn">
                  <i class="bi bi-save me-2"></i>Save Offer
                </button>
              </div>
            </div>
          </div>

          <!-- Preview Results -->
          <div class="results-card" id="resultsCard">
            <div class="results-header">
              <h3><i class="bi bi-eye me-2"></i>Discount Preview</h3>
            </div>

            <div class="results-body">
              <div class="result-item">
                <span class="result-label">Original Amount:</span>
                <span class="result-value" id="displayOriginalAmount">PKR 0</span>
              </div>
              <div class="result-item">
                <span class="result-label">Discount Offer:</span>
                <div class="discount-display">
                  <span class="discount-percentage" id="displayDiscountPercent">0%</span>
                  <span class="discount-amount" id="displayDiscountAmount">PKR 0</span>
                </div>
              </div>
              <div class="result-item">
                <span class="result-label">Final Amount:</span>
                <span class="result-value" id="displayFinalAmount">PKR 0</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Discount Offers List -->
        <div class="col-lg-7">
          <div class="offers-container">
            <div class="offers-header">
              <h3><i class="bi bi-list-ul me-2"></i>Active Discount Offers</h3>
              <div class="offers-count">
                <span id="offersCount">0 offers</span>
              </div>
            </div>

            <div class="offers-search">
              <div class="search-box">
                <i class="bi bi-search"></i>
                <input type="text" id="offerSearch" placeholder="Search offers..." onkeyup="filterOffers()">
              </div>
            </div>

            <div class="offers-list" id="offersList">
              <!-- Offers will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>

  <script>
    // Global offers array
    let discountOffers = [];

    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');

      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      toggleBtn.classList.toggle('active');

      // Toggle icon
      toggleBtn.innerText = toggleBtn.innerText === '☰' ? '✖' : '☰';
    }

    function toggleReportsMenu() {
      const subMenu = document.getElementById('reportsSubMenu');
      const reportsMenu = document.getElementById('reportsMenu');

      if (subMenu.style.display === 'none' || subMenu.style.display === '') {
        subMenu.style.display = 'block';
        reportsMenu.innerHTML = 'Reports ▲';
      } else {
        subMenu.style.display = 'none';
        reportsMenu.innerHTML = 'Reports ▼';
      }
    }

    function calculateDiscount() {
      const priceAmount = parseFloat(document.getElementById('priceAmount').value);
      const discountPercent = parseFloat(document.getElementById('discountPercent').value);

      // Validation
      if (!priceAmount || priceAmount <= 0) {
        alert('Please enter a valid amount');
        return;
      }

      if (!discountPercent || discountPercent < 0 || discountPercent > 100) {
        alert('Please enter a valid discount percentage (0-100)');
        return;
      }

      // Calculate discount
      const discountAmount = (priceAmount * discountPercent) / 100;
      const finalAmount = priceAmount - discountAmount;

      // Display results
      document.getElementById('displayOriginalAmount').textContent = `PKR ${priceAmount.toLocaleString()}`;
      document.getElementById('displayDiscountPercent').textContent = `${discountPercent}%`;
      document.getElementById('displayDiscountAmount').textContent = `PKR ${discountAmount.toFixed(2)}`;
      document.getElementById('displayFinalAmount').textContent = `PKR ${finalAmount.toFixed(2)}`;

      // Show results card
      document.getElementById('resultsCard').style.display = 'block';

      // Scroll to results
      document.getElementById('resultsCard').scrollIntoView({ behavior: 'smooth' });
    }

    function saveOffer() {
      const offerName = document.getElementById('offerName').value.trim();
      const priceAmount = parseFloat(document.getElementById('priceAmount').value);
      const discountPercent = parseFloat(document.getElementById('discountPercent').value);

      // Validation
      if (!offerName) {
        alert('Please enter an offer name');
        return;
      }

      if (!priceAmount || priceAmount <= 0) {
        alert('Please enter a valid amount');
        return;
      }

      if (!discountPercent || discountPercent < 0 || discountPercent > 100) {
        alert('Please enter a valid discount percentage (0-100)');
        return;
      }

      // Create offer object
      const newOffer = {
        id: Date.now(),
        name: offerName,
        amount: priceAmount,
        discountPercent: discountPercent,
        discountAmount: (priceAmount * discountPercent) / 100,
        finalAmount: priceAmount - (priceAmount * discountPercent) / 100,
        status: 'Active',
        createdAt: new Date().toLocaleDateString()
      };

      discountOffers.push(newOffer);
      displayOffers();
      clearForm();
      showSuccessMessage('Discount offer saved successfully!');
    }

    function displayOffers() {
      const offersList = document.getElementById('offersList');
      const offersCount = document.getElementById('offersCount');
      const offerSearch = document.getElementById('offerSearch');

      // Clear search when displaying offers
      if (offerSearch) {
        offerSearch.value = '';
      }

      offersCount.textContent = `${discountOffers.length} offer${discountOffers.length !== 1 ? 's' : ''}`;

      if (discountOffers.length === 0) {
        offersList.innerHTML = `
          <div class="empty-offers">
            <i class="bi bi-percent"></i>
            <h4>No discount offers created yet</h4>
            <p>Create your first discount offer to get started!</p>
          </div>
        `;
        return;
      }

      offersList.innerHTML = discountOffers.map(offer => `
        <div class="offer-item">
          <div class="offer-header-item">
            <h4 class="offer-name">${offer.name}</h4>
            <div class="offer-status">${offer.status}</div>
          </div>
          <div class="offer-details">
            <div class="offer-detail">
              <div class="offer-detail-label">Amount</div>
              <div class="offer-detail-value">PKR ${offer.amount.toLocaleString()}</div>
            </div>
            <div class="offer-detail">
              <div class="offer-detail-label">Discount</div>
              <div class="offer-detail-value">${offer.discountPercent}%</div>
            </div>
            <div class="offer-detail">
              <div class="offer-detail-label">Final Amount</div>
              <div class="offer-detail-value">PKR ${offer.finalAmount.toFixed(2)}</div>
            </div>
          </div>
          <div class="offer-actions">
            <button class="btn-apply" onclick="applyOffer(${offer.id})">
              <i class="bi bi-check-circle me-1"></i>Apply
            </button>
            <button class="btn-edit-offer" onclick="editOffer(${offer.id})">
              <i class="bi bi-pencil-fill me-1"></i>Edit
            </button>
            <button class="btn-delete-offer" onclick="deleteOffer(${offer.id})">
              <i class="bi bi-trash-fill me-1"></i>Delete
            </button>
          </div>
        </div>
      `).join('');
    }

    function applyOffer(id) {
      const offer = discountOffers.find(o => o.id === id);
      if (!offer) return;

      // Fill form with offer data for preview
      document.getElementById('offerName').value = offer.name;
      document.getElementById('priceAmount').value = offer.amount;
      document.getElementById('discountPercent').value = offer.discountPercent;

      // Calculate and show preview
      calculateDiscount();

      // Show success message
      showSuccessMessage(`Applied offer: ${offer.name}`);
    }

    function editOffer(id) {
      const offer = discountOffers.find(o => o.id === id);
      if (!offer) return;

      // Fill form with offer data
      document.getElementById('offerName').value = offer.name;
      document.getElementById('priceAmount').value = offer.amount;
      document.getElementById('discountPercent').value = offer.discountPercent;

      // Remove the offer (will be re-added when form is submitted)
      discountOffers = discountOffers.filter(o => o.id !== id);
      displayOffers();

      // Scroll to form
      document.querySelector('.scanner-card').scrollIntoView({ behavior: 'smooth' });
    }

    function deleteOffer(id) {
      if (confirm('Are you sure you want to delete this discount offer?')) {
        discountOffers = discountOffers.filter(o => o.id !== id);
        displayOffers();
        showSuccessMessage('Discount offer deleted successfully!');
      }
    }

    function clearForm() {
      document.getElementById('offerName').value = '';
      document.getElementById('priceAmount').value = '';
      document.getElementById('discountPercent').value = '';
      document.getElementById('resultsCard').style.display = 'none';
    }



    function filterOffers() {
      const searchTerm = document.getElementById('offerSearch').value.toLowerCase();
      const offerItems = document.querySelectorAll('.offer-item');
      let visibleCount = 0;

      offerItems.forEach(item => {
        const offerName = item.querySelector('.offer-name').textContent.toLowerCase();
        const offerAmount = item.querySelector('.offer-detail-value').textContent.toLowerCase();

        const isVisible = offerName.includes(searchTerm) || offerAmount.includes(searchTerm);

        item.style.display = isVisible ? 'block' : 'none';
        if (isVisible) visibleCount++;
      });

      // Update the offers count to show filtered results
      const offersCount = document.getElementById('offersCount');
      const totalOffers = discountOffers.length;

      if (searchTerm === '') {
        offersCount.textContent = `${totalOffers} offer${totalOffers !== 1 ? 's' : ''}`;
      } else {
        offersCount.textContent = `${visibleCount} of ${totalOffers} offer${totalOffers !== 1 ? 's' : ''}`;
      }

      // Show "no results" message if no offers match
      const offersList = document.getElementById('offersList');
      const existingNoResults = offersList.querySelector('.no-results');

      if (visibleCount === 0 && searchTerm !== '' && totalOffers > 0) {
        if (!existingNoResults) {
          const noResultsDiv = document.createElement('div');
          noResultsDiv.className = 'no-results';
          noResultsDiv.innerHTML = `
            <div class="empty-offers">
              <i class="bi bi-search"></i>
              <h4>No offers found</h4>
              <p>Try searching with different keywords</p>
            </div>
          `;
          offersList.appendChild(noResultsDiv);
        }
      } else if (existingNoResults) {
        existingNoResults.remove();
      }
    }

    function showSuccessMessage(message) {
      const successDiv = document.createElement('div');
      successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 15px 25px;
        border-radius: 10px;
        z-index: 9999;
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
      `;
      successDiv.innerHTML = `<i class="bi bi-check-circle-fill me-2"></i>${message}`;

      document.body.appendChild(successDiv);
      setTimeout(() => successDiv.remove(), 3000);
    }

    // Auto-focus on offer name input when page loads
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('offerName').focus();
      displayOffers();
    });

    // Enable buttons only when fields are filled
    function checkFormValidity() {
      const offerName = document.getElementById('offerName').value.trim();
      const priceAmount = document.getElementById('priceAmount').value;
      const discountPercent = document.getElementById('discountPercent').value;
      const calculateBtn = document.getElementById('calculateBtn');
      const saveBtn = document.getElementById('saveBtn');

      const isValidAmount = priceAmount > 0;
      const isValidPercent = discountPercent >= 0 && discountPercent <= 100;

      calculateBtn.disabled = !(isValidAmount && isValidPercent);
      saveBtn.disabled = !(offerName && isValidAmount && isValidPercent);
    }

    document.getElementById('offerName').addEventListener('input', checkFormValidity);
    document.getElementById('priceAmount').addEventListener('input', checkFormValidity);
    document.getElementById('discountPercent').addEventListener('input', checkFormValidity);
  </script>
</body>
</html>
