<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Discount Offers - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    body {
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      color: #fff;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      min-height: 100vh;
    }

    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      width: 250px;
      height: 100vh;
      background: rgba(34, 34, 34, 0.95);
      backdrop-filter: blur(10px);
      border-right: 2px solid #ffcc66;
      padding: 20px 0;
      transition: all 0.3s;
      z-index: 1000;
    }

    .sidebar a {
      display: block;
      color: #fff;
      text-decoration: none;
      padding: 15px 25px;
      transition: all 0.3s;
      border-left: 4px solid transparent;
    }

    .sidebar a:hover, .sidebar a.active {
      background: rgba(255, 204, 102, 0.1);
      border-left-color: #ffcc66;
      color: #ffcc66;
    }

    .sidebar.sidebar-collapsed {
      width: 0;
      overflow: hidden;
    }

    .toggle-btn {
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
    }

    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }

    .content {
      margin-left: 250px;
      min-height: 100vh;
      padding: 20px;
      transition: margin-left 0.3s;
    }

    .content.content-expanded {
      margin-left: 0;
    }

    /* Page Header */
    .page-header {
      text-align: center;
      margin-bottom: 40px;
      padding: 30px 0;
    }

    .page-header h1 {
      color: #ffcc66;
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 10px;
    }

    .page-header p {
      color: #aaa;
      font-size: 1.1rem;
    }

    /* Main Container */
    .discount-container {
      max-width: 800px;
      margin: 0 auto;
    }

    /* Card Scanner Section */
    .scanner-card {
      background: #2a2a2a;
      border-radius: 15px;
      border: 2px solid #ffcc66;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      overflow: hidden;
      margin-bottom: 30px;
    }

    .card-header {
      background: linear-gradient(135deg, #ffcc66, #ff9900);
      color: #000;
      padding: 20px 25px;
      font-weight: 700;
    }

    .card-header h3 {
      margin: 0;
      font-size: 1.3rem;
    }

    .card-body {
      padding: 30px 25px;
    }

    /* Input Groups */
    .input-group-custom {
      margin-bottom: 25px;
    }

    .input-group-custom label {
      display: block;
      color: #ffcc66;
      font-weight: 600;
      margin-bottom: 8px;
      font-size: 0.95rem;
    }

    .form-input {
      width: 100%;
      padding: 15px;
      background: #1a1a1a;
      border: 2px solid #444;
      border-radius: 8px;
      color: #fff;
      font-size: 16px;
      transition: all 0.3s ease;
    }

    .form-input:focus {
      outline: none;
      border-color: #ffcc66;
      background: #222;
    }

    /* Card Scanner Input */
    .card-scanner {
      position: relative;
    }

    .scanner-input {
      padding-left: 50px;
      font-family: 'Courier New', monospace;
      font-size: 18px;
      letter-spacing: 2px;
    }

    .scanner-icon {
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #ffcc66;
      font-size: 1.5rem;
    }

    /* Price Input */
    .price-input {
      padding-left: 50px;
      font-size: 18px;
      font-weight: 600;
    }

    .price-icon {
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #ffcc66;
      font-size: 1.2rem;
    }

    /* Calculate Button */
    .calculate-btn {
      width: 100%;
      background: linear-gradient(135deg, #ffcc66, #ff9900);
      border: none;
      color: #000;
      padding: 15px;
      border-radius: 8px;
      font-size: 1.1rem;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .calculate-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255,204,102,0.4);
    }

    .calculate-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    /* Results Section */
    .results-card {
      background: #2a2a2a;
      border-radius: 15px;
      border: 2px solid #28a745;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      overflow: hidden;
      display: none;
    }

    .results-header {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: #fff;
      padding: 20px 25px;
      font-weight: 700;
    }

    .results-body {
      padding: 30px 25px;
    }

    .result-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid #444;
    }

    .result-item:last-child {
      border-bottom: none;
      font-size: 1.2rem;
      font-weight: 700;
      color: #28a745;
    }

    .result-label {
      color: #aaa;
      font-weight: 500;
    }

    .result-value {
      color: #fff;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .discount-display {
      display: flex;
      gap: 15px;
      align-items: center;
    }

    .discount-percentage {
      background: #ff6b6b;
      color: #fff;
      padding: 8px 15px;
      border-radius: 20px;
      font-weight: 700;
      font-size: 1.1rem;
    }

    .discount-amount {
      background: #28a745;
      color: #fff;
      padding: 8px 15px;
      border-radius: 20px;
      font-weight: 700;
      font-size: 1.1rem;
    }

    /* Topup Button */
    .topup-btn {
      width: 100%;
      background: linear-gradient(135deg, #28a745, #20c997);
      border: none;
      color: #fff;
      padding: 15px;
      border-radius: 8px;
      font-size: 1.1rem;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-top: 20px;
    }

    .topup-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(40,167,69,0.4);
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
        padding-top: 60px;
      }
      .content {
        margin-left: 0 !important;
      }
      .discount-container {
        padding: 0 15px;
      }
      .page-header h1 {
        font-size: 2rem;
      }
      .discount-display {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
      }
      .discount-percentage, .discount-amount {
        font-size: 1rem;
        padding: 6px 12px;
      }
    }
  </style>
</head>

<body>
  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="./search.html">Search Customer</a>
    <a href="#" id="reportsMenu" onclick="toggleReportsMenu()">Reports ▼</a>
    <div id="reportsSubMenu" style="display:none; margin-left:20px;">
      <a href="./sale-report.html">Sale Report</a>
      <a href="./return-report.html">Return Report</a>
      <a href="./individual-report.html">Individual Report</a>
    </div>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./add-ride.html">Add Ride</a>
    <a href="./discount-offers.html" class="active">Discount Offers</a>
    <a href="./setting.html">Settings</a>
  </div>

  <div class="content" id="content">
    <div class="discount-container">
      
      <!-- Page Header -->
      <div class="page-header">
        <h1><i class="bi bi-percent me-3"></i>Discount Offers</h1>
        <p>Enter amount and get instant discount offers</p>
      </div>

      <!-- Amount Offer Section -->
      <div class="scanner-card">
        <div class="card-header">
          <h3><i class="bi bi-calculator me-2"></i>Amount Offer Calculator</h3>
        </div>

        <div class="card-body">
          <!-- Price Input -->
          <div class="input-group-custom">
            <label>Enter Amount (PKR)</label>
            <div class="card-scanner">
              <i class="bi bi-currency-dollar price-icon"></i>
              <input type="number" id="priceAmount" class="form-input price-input" placeholder="0" min="0" step="0.01">
            </div>
          </div>

          <!-- Calculate Button -->
          <button type="button" onclick="calculateDiscount()" class="calculate-btn" id="calculateBtn">
            <i class="bi bi-percent me-2"></i>Get Discount Offer
          </button>
        </div>
      </div>

      <!-- Results Section -->
      <div class="results-card" id="resultsCard">
        <div class="results-header">
          <h3><i class="bi bi-gift me-2"></i>Your Discount Offer</h3>
        </div>

        <div class="results-body">
          <div class="result-item">
            <span class="result-label">Original Amount:</span>
            <span class="result-value" id="displayOriginalAmount">PKR 0</span>
          </div>
          <div class="result-item">
            <span class="result-label">Discount Offer:</span>
            <div class="discount-display">
              <span class="discount-percentage" id="displayDiscountPercent">0%</span>
              <span class="discount-amount" id="displayDiscountAmount">PKR 0</span>
            </div>
          </div>
          <div class="result-item">
            <span class="result-label">Final Amount:</span>
            <span class="result-value" id="displayFinalAmount">PKR 0</span>
          </div>
        </div>
      </div>
      
    </div>
  </div>

  <script>
    // Discount rules based on amount ranges
    const discountRules = [
      { minAmount: 0, maxAmount: 500, discount: 5 },
      { minAmount: 501, maxAmount: 1000, discount: 10 },
      { minAmount: 1001, maxAmount: 2000, discount: 15 },
      { minAmount: 2001, maxAmount: 5000, discount: 20 },
      { minAmount: 5001, maxAmount: Infinity, discount: 25 }
    ];

    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');

      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      toggleBtn.classList.toggle('active');

      // Toggle icon
      toggleBtn.innerText = toggleBtn.innerText === '☰' ? '✖' : '☰';
    }

    function toggleReportsMenu() {
      const subMenu = document.getElementById('reportsSubMenu');
      const reportsMenu = document.getElementById('reportsMenu');

      if (subMenu.style.display === 'none' || subMenu.style.display === '') {
        subMenu.style.display = 'block';
        reportsMenu.innerHTML = 'Reports ▲';
      } else {
        subMenu.style.display = 'none';
        reportsMenu.innerHTML = 'Reports ▼';
      }
    }

    function calculateDiscount() {
      const priceAmount = parseFloat(document.getElementById('priceAmount').value);

      // Validation
      if (!priceAmount || priceAmount <= 0) {
        alert('Please enter a valid amount');
        return;
      }

      // Find applicable discount
      const applicableRule = discountRules.find(rule =>
        priceAmount >= rule.minAmount && priceAmount <= rule.maxAmount
      );

      const discountPercent = applicableRule ? applicableRule.discount : 0;
      const discountAmount = (priceAmount * discountPercent) / 100;
      const finalAmount = priceAmount - discountAmount;

      // Display results
      document.getElementById('displayOriginalAmount').textContent = `PKR ${priceAmount.toLocaleString()}`;
      document.getElementById('displayDiscountPercent').textContent = `${discountPercent}%`;
      document.getElementById('displayDiscountAmount').textContent = `PKR ${discountAmount.toFixed(2)}`;
      document.getElementById('displayFinalAmount').textContent = `PKR ${finalAmount.toFixed(2)}`;

      // Show results card
      document.getElementById('resultsCard').style.display = 'block';

      // Scroll to results
      document.getElementById('resultsCard').scrollIntoView({ behavior: 'smooth' });
    }



    function showSuccessMessage(message) {
      const successDiv = document.createElement('div');
      successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 15px 25px;
        border-radius: 10px;
        z-index: 9999;
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
      `;
      successDiv.innerHTML = `<i class="bi bi-check-circle-fill me-2"></i>${message}`;
      
      document.body.appendChild(successDiv);
      setTimeout(() => successDiv.remove(), 3000);
    }

    // Auto-focus on amount input when page loads
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('priceAmount').focus();
    });

    // Enable calculate button only when amount is filled
    function checkFormValidity() {
      const priceAmount = document.getElementById('priceAmount').value;
      const calculateBtn = document.getElementById('calculateBtn');

      calculateBtn.disabled = !(priceAmount > 0);
    }

    document.getElementById('priceAmount').addEventListener('input', checkFormValidity);
  </script>
</body>
</html>
